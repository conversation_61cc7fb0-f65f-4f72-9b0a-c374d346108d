-- revision: '20250101000000_add_pms_department_management_tables'
-- down_revision: '20250415020832_adding_audits'

-- upgrade


CREATE TABLE department (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    parent_id integer,
    name character varying(255) NOT NULL,
    code character varying(50) NOT NULL UNIQUE,
    description text,
    financial_code character varying(20),
    is_active boolean DEFAULT true,
    auto_create_on_property_launch boolean DEFAULT false,
    config jsonb,
    CONSTRAINT department_pkey PRIMARY KEY (id),
    CONSTRAINT department_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES department(id)
);

CREATE TABLE brand_department_mapping (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    brand_id integer NOT NULL,
    department_id integer NOT NULL,
    is_active boolean DEFAULT true,
    CONSTRAINT brand_department_mapping_pkey PRIMARY KEY (id),
    CONSTRAINT brand_department_mapping_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brand(id),
    CONSTRAINT brand_department_mapping_department_id_fkey FOREIGN KEY (department_id) REFERENCES department(id),
    CONSTRAINT brand_department_mapping_brand_id_department_id_key UNIQUE (brand_id, department_id)
);

CREATE INDEX idx_brand_department_brand_id ON brand_department_mapping(brand_id);
CREATE INDEX idx_brand_department_dept_id ON brand_department_mapping(department_id);

CREATE TABLE property_department (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    property_id character varying NOT NULL,
    tenant_department_id integer,
    inherited_from_brand_id integer,
    parent_id integer,
    name character varying(255) NOT NULL,
    code character varying(50) NOT NULL,
    description text,
    financial_code character varying(20),
    is_active boolean DEFAULT true,
    is_custom boolean DEFAULT false,
    config jsonb,
    CONSTRAINT property_department_pkey PRIMARY KEY (id),
    CONSTRAINT property_department_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id),
    CONSTRAINT property_department_tenant_department_id_fkey FOREIGN KEY (tenant_department_id) REFERENCES department(id),
    CONSTRAINT property_department_inherited_from_brand_id_fkey FOREIGN KEY (inherited_from_brand_id) REFERENCES brand(id),
    CONSTRAINT property_department_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES property_department(id),
    CONSTRAINT property_department_property_id_code_key UNIQUE (property_id, code)
);

CREATE INDEX idx_property_department_property_id ON property_department(property_id);
CREATE INDEX idx_property_department_tenant_dept ON property_department(tenant_department_id);

CREATE TABLE sku_department_mapping (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    sku_id character varying NOT NULL,
    department_id integer NOT NULL,
    effective_from timestamp,
    effective_to timestamp,
    is_active boolean DEFAULT true,
    CONSTRAINT sku_department_mapping_pkey PRIMARY KEY (id),
    CONSTRAINT sku_department_mapping_sku_id_fkey FOREIGN KEY (sku_id) REFERENCES sku(id),
    CONSTRAINT sku_department_mapping_department_id_fkey FOREIGN KEY (department_id) REFERENCES department(id),
    CONSTRAINT sku_department_mapping_sku_id_department_id_key UNIQUE (sku_id, department_id)
);

CREATE INDEX idx_sku_department_sku_id ON sku_department_mapping(sku_id);
CREATE INDEX idx_sku_department_dept_id ON sku_department_mapping(department_id);

CREATE TABLE property_sku_department (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    property_id character varying NOT NULL,
    sku_id character varying NOT NULL,
    property_department_id integer NOT NULL,
    effective_from timestamp,
    effective_to timestamp,
    is_active boolean DEFAULT true,
    CONSTRAINT property_sku_department_pkey PRIMARY KEY (id),
    CONSTRAINT property_sku_department_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id),
    CONSTRAINT property_sku_department_sku_id_fkey FOREIGN KEY (sku_id) REFERENCES sku(id),
    CONSTRAINT property_sku_department_property_department_id_fkey FOREIGN KEY (property_department_id) REFERENCES property_department(id),
    CONSTRAINT property_sku_department_property_id_sku_id_property_department_id_key UNIQUE (property_id, sku_id, property_department_id)
);

CREATE INDEX idx_prop_sku_dept_property_id ON property_sku_department(property_id);
CREATE INDEX idx_prop_sku_dept_sku_id ON property_sku_department(sku_id);
CREATE INDEX idx_prop_sku_dept_dept_id ON property_sku_department(property_department_id);


CREATE TABLE seller_template (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    name character varying(255) NOT NULL,
    code character varying(50) NOT NULL UNIQUE,
    description text,
    default_department_id integer,
    is_active boolean DEFAULT true,
    auto_create_on_property_launch boolean DEFAULT false,
    template_config jsonb,
    CONSTRAINT seller_template_pkey PRIMARY KEY (id),
    CONSTRAINT seller_template_default_department_id_fkey FOREIGN KEY (default_department_id) REFERENCES department(id)
);

CREATE TABLE sku_terminal_type_assignment (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    sku_id character varying NOT NULL,
    terminal_type_template_id integer NOT NULL,
    is_active boolean DEFAULT true,
    CONSTRAINT sku_terminal_type_assignment_pkey PRIMARY KEY (id),
    CONSTRAINT sku_terminal_type_assignment_sku_id_fkey FOREIGN KEY (sku_id) REFERENCES sku(id),
    CONSTRAINT sku_terminal_type_assignment_terminal_type_template_id_fkey FOREIGN KEY (terminal_type_template_id) REFERENCES seller_template(id),
    CONSTRAINT sku_terminal_type_assignment_sku_id_terminal_type_template_id_key UNIQUE (sku_id, terminal_type_template_id)
);

CREATE INDEX idx_sku_terminal_type_sku_id ON sku_terminal_type_assignment(sku_id);
CREATE INDEX idx_sku_terminal_type_template_id ON sku_terminal_type_assignment(terminal_type_template_id);


CREATE TABLE transaction_master (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    transaction_code character varying(50) NOT NULL UNIQUE,
    name character varying(255) NOT NULL,
    entity_type character varying(50) NOT NULL,
    transaction_type character varying(50) NOT NULL,
    operational_unit_id character varying,
    operational_unit_type character varying(50),
    source character varying(100),
    gl_code character varying(20),
    erp_id character varying(50),
    is_merge boolean DEFAULT false,
    particulars text,
    status character varying(20) DEFAULT 'ACTIVE',
    hotel_entity_id character varying,
    franchiser_entity_id character varying,
    usali_code character varying(20),
    usali_category character varying(100),
    CONSTRAINT transaction_master_pkey PRIMARY KEY (id)
);

CREATE INDEX idx_transaction_master_code ON transaction_master(transaction_code);
CREATE INDEX idx_transaction_master_entity_type ON transaction_master(entity_type);
CREATE INDEX idx_transaction_master_operational_unit ON transaction_master(operational_unit_id, operational_unit_type);
CREATE INDEX idx_transaction_master_status ON transaction_master(status);

CREATE TABLE transaction_default_mapping (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    triggering_entity_type character varying(50) NOT NULL,
    triggering_entity_category character varying(100) NOT NULL,
    entity_type character varying(20) NOT NULL,
    default_gl_code character varying(50),
    default_erp_id character varying(50),
    default_particulars text,
    default_is_merge boolean DEFAULT true,
    is_active boolean DEFAULT true,
    CONSTRAINT transaction_default_mapping_pkey PRIMARY KEY (id),
    CONSTRAINT transaction_default_mapping_triggering_entity_type_triggering_entity_category_entity_type_key UNIQUE (triggering_entity_type, triggering_entity_category, entity_type)
);

CREATE INDEX idx_transaction_default_mapping_entity_type ON transaction_default_mapping(triggering_entity_type);

CREATE TABLE payment_method (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    name character varying(255) NOT NULL,
    code character varying(50) NOT NULL UNIQUE,
    payment_method_type character varying(100),
    paid_to character varying(100),
    allowed_paid_by character varying(100),
    auto_create_on_property_launch boolean DEFAULT false,
    config jsonb,
    is_active boolean DEFAULT true,
    CONSTRAINT payment_method_pkey PRIMARY KEY (id)
);

CREATE TABLE property_payment_method (
    created_at timestamptz,
    modified_at timestamptz,
    id serial NOT NULL,
    property_id character varying NOT NULL,
    payment_method_id integer,
    name character varying(255) NOT NULL,
    code character varying(50) NOT NULL,
    is_custom boolean DEFAULT false,
    config jsonb,
    is_active boolean DEFAULT true,
    CONSTRAINT property_payment_method_pkey PRIMARY KEY (id),
    CONSTRAINT property_payment_method_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id),
    CONSTRAINT property_payment_method_payment_method_id_fkey FOREIGN KEY (payment_method_id) REFERENCES payment_method(id),
    CONSTRAINT property_payment_method_property_id_code_key UNIQUE (property_id, code)
);

CREATE INDEX idx_property_payment_method_property_id ON property_payment_method(property_id);
CREATE INDEX idx_property_payment_method_payment_method_id ON property_payment_method(payment_method_id);

ALTER TABLE property ADD COLUMN brand_id integer;
ALTER TABLE property ADD COLUMN manage_franchiser_finance boolean DEFAULT false;
ALTER TABLE property ADD CONSTRAINT property_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brand(id);

CREATE INDEX idx_property_brand_id ON property(brand_id);
CREATE INDEX idx_property_franchiser_finance ON property(manage_franchiser_finance) WHERE manage_franchiser_finance = true;

ALTER TABLE seller ADD COLUMN default_department_id integer;
ALTER TABLE seller ADD COLUMN property_terminal_id integer;
ALTER TABLE seller ADD COLUMN is_auto_created boolean DEFAULT false;
ALTER TABLE seller ADD CONSTRAINT seller_default_department_id_fkey FOREIGN KEY (default_department_id) REFERENCES property_department(id);
ALTER TABLE seller ADD CONSTRAINT seller_property_terminal_id_fkey FOREIGN KEY (property_terminal_id) REFERENCES seller_template(id);

CREATE INDEX idx_seller_default_department ON seller(default_department_id);
CREATE INDEX idx_seller_terminal_type ON seller(property_terminal_id);

ALTER TABLE sku ADD COLUMN seller_template_id integer;
ALTER TABLE sku ADD CONSTRAINT sku_seller_template_id_fkey FOREIGN KEY (seller_template_id) REFERENCES seller_template(id);

CREATE INDEX idx_sku_seller_template ON sku(seller_template_id);

ALTER TABLE property_sku ADD COLUMN seller_id character varying;
ALTER TABLE property_sku ADD CONSTRAINT property_sku_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id);
ALTER TABLE property_sku ADD CONSTRAINT property_sku_seller_id_unique UNIQUE (seller_id);

CREATE INDEX idx_property_sku_seller ON property_sku(seller_id);

-- downgrade

DROP INDEX IF EXISTS idx_property_sku_seller;
ALTER TABLE property_sku DROP CONSTRAINT IF EXISTS property_sku_seller_id_unique;
ALTER TABLE property_sku DROP CONSTRAINT IF EXISTS property_sku_seller_id_fkey;
ALTER TABLE property_sku DROP COLUMN IF EXISTS seller_id;

DROP INDEX IF EXISTS idx_sku_seller_template;
ALTER TABLE sku DROP CONSTRAINT IF EXISTS sku_seller_template_id_fkey;
ALTER TABLE sku DROP COLUMN IF EXISTS seller_template_id;

DROP INDEX IF EXISTS idx_seller_terminal_type;
DROP INDEX IF EXISTS idx_seller_default_department;
ALTER TABLE seller DROP CONSTRAINT IF EXISTS seller_property_terminal_id_fkey;
ALTER TABLE seller DROP CONSTRAINT IF EXISTS seller_default_department_id_fkey;
ALTER TABLE seller DROP COLUMN IF EXISTS is_auto_created;
ALTER TABLE seller DROP COLUMN IF EXISTS property_terminal_id;
ALTER TABLE seller DROP COLUMN IF EXISTS default_department_id;

DROP INDEX IF EXISTS idx_property_franchiser_finance;
DROP INDEX IF EXISTS idx_property_brand_id;
ALTER TABLE property DROP CONSTRAINT IF EXISTS property_brand_id_fkey;
ALTER TABLE property DROP COLUMN IF EXISTS manage_franchiser_finance;
ALTER TABLE property DROP COLUMN IF EXISTS brand_id;

-- Drop payment method tables
DROP INDEX IF EXISTS idx_property_payment_method_payment_method_id;
DROP INDEX IF EXISTS idx_property_payment_method_property_id;
DROP TABLE IF EXISTS property_payment_method;
DROP TABLE IF EXISTS payment_method;

-- Drop financial tables
DROP INDEX IF EXISTS idx_transaction_default_mapping_entity_type;
DROP TABLE IF EXISTS transaction_default_mapping;

DROP INDEX IF EXISTS idx_transaction_master_status;
DROP INDEX IF EXISTS idx_transaction_master_operational_unit;
DROP INDEX IF EXISTS idx_transaction_master_entity_type;
DROP INDEX IF EXISTS idx_transaction_master_code;
DROP TABLE IF EXISTS transaction_master;

-- Drop terminal type tables
DROP INDEX IF EXISTS idx_sku_terminal_type_template_id;
DROP INDEX IF EXISTS idx_sku_terminal_type_sku_id;
DROP TABLE IF EXISTS sku_terminal_type_assignment;
DROP TABLE IF EXISTS seller_template;

-- Drop department tables
DROP INDEX IF EXISTS idx_prop_sku_dept_dept_id;
DROP INDEX IF EXISTS idx_prop_sku_dept_sku_id;
DROP INDEX IF EXISTS idx_prop_sku_dept_property_id;
DROP TABLE IF EXISTS property_sku_department;

DROP INDEX IF EXISTS idx_sku_department_dept_id;
DROP INDEX IF EXISTS idx_sku_department_sku_id;
DROP TABLE IF EXISTS sku_department_mapping;

DROP INDEX IF EXISTS idx_property_department_tenant_dept;
DROP INDEX IF EXISTS idx_property_department_property_id;
DROP TABLE IF EXISTS property_department;

DROP INDEX IF EXISTS idx_brand_department_dept_id;
DROP INDEX IF EXISTS idx_brand_department_brand_id;
DROP TABLE IF EXISTS brand_department_mapping;

DROP TABLE IF EXISTS department;
