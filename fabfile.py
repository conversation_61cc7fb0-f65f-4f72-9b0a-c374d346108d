# pylint: skip-file
# flake8: noqa
from fabric.api import env, run, sudo, local, put, lcd, task
from fabric.contrib.files import exists
from fabric.operations import require

from script_path import gunicorn_script


@task
def push_image(tag_name, version):
    local('docker push %(DOCKER_REGISTRY)s/%(tag_name)s:%(version)s' % {
                'DOCKER_REGISTRY': env.DOCKER_REGISTRY,
                'tag_name': tag_name, 'version': version
            })

@task
def build(tag_name, version):
    require('env_file', provided_by=(prod, dev, env.run,))
    local('docker build --build-arg req_file=%(req_file)s -t %(docker_registry)s/%(tag_name)s:%(version)s '\
          '-t %(docker_registry)s/%(tag_name)s:latest .' % {
                'version': version, 'docker_registry': env.DOCKER_REGISTRY,
                'tag_name': tag_name, 'req_file': env.req_file
              })
    push_image(tag_name, version)

@task
def tag_image(image_id, tag_name, version):
    require('env_file', provided_by=(prod, dev, env.run,))
    local('docker tag %(image_id)s %(DOCKER_REGISTRY)s/%(tag_name)s:%(version)s' % {
                'DOCKER_REGISTRY': env.DOCKER_REGISTRY,
                'image_id': image_id, 'tag_name': tag_name,
                'version': version
            })

@task
def pull_image(tag_name, version):
    env.sudo('docker pull %(DOCKER_REGISTRY)s/%(tag_name)s:%(version)s' % {
                'DOCKER_REGISTRY': env.DOCKER_REGISTRY,
                'tag_name': tag_name, 'version': version
            })

@task
def create_network(network):
    env.sudo('docker network create -d bridge %(network)s' % {'network': network})

@task
def create_gunicorn_container(tag_name, version, port, network=None, app_version='1.0'):
    docker_options = env.copy()
    docker_options.update({'tag_name': tag_name, 'version': version, 'port': port, 'script_path': gunicorn_script,
                           'app_version': app_version})
    if network:
        docker_options['network'] = network
        env.sudo(
            'docker run -e APP_VERSION=\'%(app_version)s\' -v %(log_root)s:/var/log/  --name cataloging_service_gunicorn -p %(port)s:8001 --network=%(network)s ' \
            '--env-file=%(env_file)s -itd %(DOCKER_REGISTRY)s/%(tag_name)s:%(version)s ' \
            '%(script_path)s' % docker_options)
    else:
        env.sudo(
            'docker run -e APP_VERSION=\'%(app_version)s\' -v %(log_root)s:/var/log/ --name cataloging_service_gunicorn -p %(port)s:8001 ' \
            '--env-file=%(env_file)s -itd %(DOCKER_REGISTRY)s/%(tag_name)s:%(version)s ' \
            '%(script_path)s' % docker_options)

def start_containers():
    env.sudo('docker start cataloging_service_gunicorn')

@task
def stop_containers(tag_name):
    env.tag_name = tag_name
    env.sudo('docker ps | grep "%(tag_name)s" | awk \'{print $1}\' | xargs -I {} docker stop {}' % env)

@task
def remove_containers(tag_name):
    env.tag_name = tag_name
    env.sudo('docker ps -a | grep "%(tag_name)s" | awk \'{print $1}\' | xargs -I {} docker rm {}' % env)

@task
def setup_postgres(network, postgres_user='treeboadmin', postgres_pwd='treeboadmin', postgres_db='cataloging_service', port=6432, name='postgres'):
    env.network = network
    env.postgres_user = postgres_user
    env.postgres_pwd = postgres_pwd
    env.postgres_db = postgres_db
    env.name=name
    env.port = port
    env.sudo('docker run --net=%(network)s --name %(name)s -p %(port)s:5432 '\
          '-e POSTGRES_PASSWORD=%(postgres_pwd)s -e POSTGRES_USER=%(postgres_user)s '\
          '-e POSTGRES_DB=%(postgres_db)s -d postgres' % env)

@task
def setup_rabbitmq(network):
    env.sudo('docker run --net=%(network)s --name rabbitmq -p 15672:15672 -p 5672:5672 -d rabbitmq' % {'network': network})

@task
def prod():
    require('hosts')
    env.run = run
    env.sudo = sudo
    env.req_file = 'requirements.txt'
    env.env_file = '/etc/cataloging-service/docker_env/prod.env'
    if not exists('/etc/cataloging-service/docker_env/prod.env'):
        abort('Docker environment file not setup. Please add docker environment on path: "/etc/cataloging-service/docker_env/prod.env"')

@task
def dev():
    require('hosts')
    env.run = run
    env.sudo = sudo
    env.req_file = 'requirements/staging.txt'
    env.env_file = '/treebo/docker_env/cataloging-service/dev.env'
    if not exists('/treebo/docker_env/cataloging-service'):
        env.sudo('mkdir -p /treebo/docker_env/cataloging-service')
    put('docker_env/*', '/treebo/docker_env/cataloging-service/', use_sudo=True)

@task
def localhost():
    env.run = local
    env.cd = lcd
    env.sudo = local
    env.req_file = 'requirements.txt'
    env.env_file = 'docker_env/local.env'

@task
def deploy(tag_name, version, port, network=None, user=None, app_version='1.0'):
    if user:
        env.user = user
    require('env_file', provided_by=(prod, dev, localhost,))
    build(tag_name, version)
    pull_image(tag_name, version)
    stop_services(tag_name)
    remove_containers(tag_name)
    create_gunicorn_container(tag_name, version, port, network=network, app_version=app_version)

@task
def rollback_to_version(tag_name, version, port, user=None, app_version='1.0'):
    if user:
        env.user = user
    require('env_file', provided_by=(prod, dev, localhost,))
    pull_image(tag_name, version)
    stop_services(tag_name)
    remove_containers(tag_name)
    create_gunicorn_container(tag_name, version, port, app_version=app_version)

@task
def start_services():
    require('env_file', provided_by=(prod, dev, localhost,))
    start_containers()

@task
def stop_services(tag_name):
    require('env_file', provided_by=(prod, dev, localhost,))
    stop_containers(tag_name)

@task
def restart_services(tag_name):
    require('env_file', provided_by=(prod, dev, localhost,))
    stop_services(tag_name)
    start_services()

# Below commands should be used on local machine only, to speed up docker clean up process.
@task
def remove_stopped_containers():
    local("docker rm $(docker ps -a | grep Exited | awk '{print $1}')")

@task
def remove_untagged_containers():
    local('docker rmi $(docker images -q --filter "dangling=true")')

@task
def stop_and_remove_all_containers():
    local('docker rm -f $(docker ps -a -q)')

@task
def test_fabric():
    local('echo "Hello There!!"')
