import datetime as dtime

from sqlalchemy.dialects.postgresql.json import JSONB

from flask_login import UserMixin
from flask_security.core import RoleMixin
from sqlalchemy.dialects.postgresql import JSON, BYTEA, ARRAY
from sqlalchemy import Enum, DateTime, Integer, NUMERIC, String, Boolean, DECIMAL, Text, Date, Time, and_, func, \
    Interval
from sqlalchemy.orm import backref, relationship
from sqlalchemy.sql import expression
from sqlalchemy.sql.schema import PrimaryKeyConstraint
from sqlalchemy.sql.schema import UniqueConstraint, Table, Column, ForeignKey, Index
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base
from treebo_commons.utils.config_value_parser import IntegerParser, StringParser, JsonParser, BooleanParser, \
    ListParser, DateParser

from cataloging_service.constants import constants
from cataloging_service.constants.model_choices import PropertyChoices, BankDetailChoices, PropertyDetailChoices, \
    GoogleDriveFileTypeChoices, RoomTypeConfigurationChoices, OwnerChoices, ParkingChoices, \
    SwimmingPoolChoices, BreakfastChoices, TvChoices, AcChoices, StoveChoices, RoomAmenityChoices, \
    TransportStationChoices, StandardStatusChoices, TaxTypeChoices, SkuTypeChoices, SellerTypeModelChoices, \
    SuperHeroPriceSlabChoices, HygieneShieldNameChoices, FoodTypeChoices
from cataloging_service.domain.enums import ConfigValueType

python_property = property  # shitty stuff. Because the models have a field `property` unable to use @property decorator


class TestObjectMixin(object):
    is_test = Column(Boolean, default=False, nullable=False, server_default=expression.false())


class TimeStampMixin(object):
    created_at = Column(DateTime(timezone=True), default=func.now())
    modified_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())


class DeleteMixin(object):
    is_deleted = Column('is_deleted', Boolean, default=False)

    def delete(self):
        self.is_deleted = True
        return self


class Policy(object):
    id = Column('id', Integer, primary_key=True)
    policy_type = Column('policy_type', String(80), nullable=False)
    title = Column('title', String(80), nullable=False)
    description = Column('description', String(800), nullable=False)
    display_in_need_to_know = Column('display_in_need_to_know', Boolean, default=False)
    display_in_policy = Column('display_in_policy', Boolean, default=False)


guest_type_property = Table('guest_type_property', Base.metadata,
                            Column('id', Integer, primary_key=True),
                            Column('guest_type_id', ForeignKey('guest_type.id'), nullable=False),
                            Column('property_id', ForeignKey('property.id'), nullable=False))


class GuestTypeProperty(Base):
    __table__ = guest_type_property

    guest_type = relationship("GuestType", backref="guest_type_properties")
    property = relationship("Property", backref="guest_type_properties")


breakfast_cuisine = Table('breakfast_cuisine', Base.metadata,
                          Column('id', Integer, primary_key=True),
                          Column('cuisine_id', ForeignKey('cuisine.id'), nullable=False),
                          Column('breakfast_id', ForeignKey('amenity_breakfast.id'), nullable=False))

restaurant_cuisine = Table('restaurant_cuisine', Base.metadata,
                           Column('id', Integer, primary_key=True),
                           Column('cuisine_id', ForeignKey('cuisine.id'), nullable=False),
                           Column('restaurant_id', ForeignKey('restaurant.id'), nullable=False))

roles_users = Table('roles_users', Base.metadata,
                    Column('id', Integer, primary_key=True),
                    Column('user_id', Integer(), ForeignKey('cs_user.id')),
                    Column('role_id', Integer(), ForeignKey('role.id')))

properties_sku_categories = Table('properties_sku_categories', Base.metadata,
                                  Column('id', Integer, primary_key=True, autoincrement=True),
                                  Column('property_id', String, ForeignKey('property.id'), nullable=False),
                                  Column('sku_category_id', Integer, ForeignKey('sku_category.id'), nullable=False))


class PropertiesSkuCategories(Base):
    __table__ = properties_sku_categories

    property = relationship('Property', backref='properties_sku_categories')
    sku_category = relationship('SkuCategory', backref='properties_sku_categories')


class Role(Base, RoleMixin, TimeStampMixin):
    __tablename__ = 'role'

    id = Column(Integer(), primary_key=True)
    name = Column(String(80), unique=True, nullable=False)
    description = Column(String(255))

    def __str__(self):
        return str(self.name)


class User(Base, TimeStampMixin, UserMixin):
    __tablename__ = 'cs_user'

    id = Column(Integer, primary_key=True)
    email = Column(String(255), unique=True, nullable=False)
    password = Column(String(255), nullable=False)
    active = Column(Boolean(), default=True)
    roles = relationship('Role', secondary=roles_users,
                         backref=backref('users', lazy='dynamic'))

    def __str__(self):
        return str(self.email)

    def is_authorized(self, permitted_roles=None):
        if not permitted_roles:
            permitted_roles = constants.ADMIN_VIEW_ACCESSIBLE_ROLES
        for role in self.roles:
            if role.name in permitted_roles:
                return True

        return False


class Property(Base, TimeStampMixin, TestObjectMixin):
    """
    Remember doing a <property_object>.add_skus, sends an after_update signal on Property model object
    No need to do the following:
    self.property_bundles.append(PropertyBundle(property=self, bundle=bundle, saleable=saleable, status=status))
    as the following:
    PropertyBundle(property=self, bundle=bundle, saleable=saleable, status=status) will automatically
    attach the object to self.property_bundles
    :param items:
    :return:
    """
    __tablename__ = 'property'

    id = Column('id', String, primary_key=True)
    hx_id = Column('hx_id', String(10), unique=True)
    external_hotel_id = Column('external_hotel_id', String,
                               doc="Hotel identifier used by tenants, mostly for reporting purpose.")
    status = Column('status', Enum(*PropertyChoices.STATUS_CHOICES, name='status_choices'),
                    default=PropertyChoices.STATUS_NEAR_CONFIRMED)
    name = Column('name', String(100), unique=True)
    old_name = Column('old_name', String(100), nullable=False)
    legal_name = Column('legal_name', String(100))
    signed_date = Column('signed_date', Date)
    contractual_launch_date = Column('contractual_launch_date', Date)
    launched_date = Column('launched_date', Date)
    churned_date = Column('churned_date', Date)
    base_currency_code = Column('base_currency_code', String)
    timezone = Column('timezone', String)
    country_code = Column('country_code', String)
    logo = Column('logo', String(1000))
    current_business_date = Column('current_business_date', Date)

    # Department architecture fields (will be added via migration)
    brand_id = Column('brand_id', Integer, ForeignKey('brand.id'), nullable=True)
    manage_franchiser_finance = Column('manage_franchiser_finance', Boolean, default=False)

    sku_categories = relationship('SkuCategory', secondary=properties_sku_categories,
                                  backref=backref('properties', lazy=True), cascade='delete')
    sku_s = relationship('Sku', secondary='property_sku', backref=backref('properties'))
    brands = relationship('Brand', secondary='property_brand')
    primary_brand = relationship('Brand', foreign_keys=[brand_id], backref='primary_properties')

    property_detail = relationship('PropertyDetail', back_populates='property', innerjoin=True, uselist=False)
    location = relationship('Location', back_populates='property', innerjoin=True, uselist=False)
    guest_facing_process = relationship('GuestFacingProcess', back_populates='property', uselist=False,
                                        innerjoin=True)
    description = relationship('Description', back_populates='property', uselist=False)
    neighbouring_place = relationship('NeighbouringPlace', back_populates='property', uselist=False)
    amenity_summary = relationship('AmenitySummary', back_populates='property', uselist=False, innerjoin=True)
    cost_center_id = Column('cost_center_id', Text, nullable=True)
    region = Column('region', Text, nullable=True)

    def __str__(self):
        return str.format('%s: %s/%s' % (self.id, self.name, self.old_name))

    def __repr__(self):
        return '<Property {}>'.format(self.id + " " + self.name)


class SellerTypeHistory(Base, TimeStampMixin):
    __tablename__ = 'seller_type_history'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', backref=backref('seller_type_model_history'), innerjoin=True)
    from_seller_type = Column('from_seller_model', Enum(SellerTypeModelChoices, name='seller_type_choices'),
                              nullable=False)
    to_seller_type = Column('to_seller_model', Enum(SellerTypeModelChoices, name='seller_type_choices'),
                            nullable=False)
    date = Column('date', Date, nullable=False)


class BankDetail(Base, TimeStampMixin):
    __tablename__ = 'bank_detail'

    id = Column('id', Integer, primary_key=True)
    account_name = Column('account_name', String(100), nullable=False)
    account_number = Column('account_number', String(50), nullable=False)
    account_type = Column('account_type', Enum(*BankDetailChoices.ACCOUNT_TYPE_CHOICES, name='account_type_choices'),
                          default=BankDetailChoices.CURRENT_ACCOUNT)

    ifsc_code = Column('ifsc_code', String(100))
    branch_code = Column(String)
    swift_code = Column(String)

    # bank is bank_name
    bank = Column('bank', String(100), nullable=False)
    branch = Column('branch', String(100))
    property_detail = relationship('PropertyDetail', back_populates='bank_detail', uselist=False)

    def __str__(self):
        return str.format('%s: %s, %s, %s' % (self.id, self.account_number, self.account_name, self.bank))


class PropertyPolicyMap(Base, TimeStampMixin):
    __tablename__ = 'property_policy_map'

    id = Column('id', Integer, primary_key=True)
    property_detail_id = Column('property_detail_id', Integer, ForeignKey('property_detail.id'))
    property_policy_id = Column('property_policy_id', Integer, ForeignKey('property_policy.id'))
    property_details = relationship('PropertyDetail', innerjoin=True)
    property_policy = relationship('PropertyPolicy', innerjoin=True)

    def __str__(self):
        return str.format('{property_id}-{policy_id}'.format(property_id=self.property_details.property_id,
                                                             policy_id=self.property_policy_id))


class PropertyDetail(Base, TimeStampMixin):
    __tablename__ = 'property_detail'

    id = Column('id', Integer, primary_key=True)
    # TODO: Should be unique=True?
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', back_populates='property_detail')
    neighbourhood_type = Column('neighbourhood_type',
                                Enum(*PropertyDetailChoices.NEIGHBOURHOOD_CHOICES, name='neighbourhood_choices'),
                                nullable=False)
    neighbourhood_detail = Column('neighbourhood_detail', String(500))
    property_type = Column('property_type',
                           Enum(*PropertyDetailChoices.PROPERTY_TYPE_CHOICES, name='property_type_choices'),
                           nullable=False)
    property_style = Column('property_style',
                            Enum(*PropertyDetailChoices.PROPERTY_STYLE_CHOICES, name='property_style_choices'),
                            nullable=False)
    style_detail = Column('style_detail', String(500))
    construction_year = Column('construction_year', Integer)
    building_style = Column('building_style',
                            Enum(*PropertyDetailChoices.BUILDING_CHOICES, name='building_style_choices'),
                            nullable=False)
    unmarried_couple_allowed = Column('unmarried_couple_allowed', Boolean, default=False)
    local_id_allowed = Column('local_id_allowed', Boolean, default=False)
    floor_count = Column('floor_count', Integer)
    star_rating = Column('star_rating', Integer)
    previous_franchise = Column('previous_franchise', Boolean, default=False)
    reception_landline = Column('reception_landline', String(200))
    reception_mobile = Column('reception_mobile', String(200))
    email = Column('email', String(50))
    # TODO: Should be unique=True?
    bank_detail_id = Column('bank_detail_id', ForeignKey('bank_detail.id'), nullable=True)
    bank_detail = relationship('BankDetail', back_populates='property_detail')
    vat_number = Column('vat_number', String(15))
    tin = Column('tin', String(15))
    pan = Column('pan', String(15))
    service_tax_number = Column('service_tax_number', String(15))
    luxury_tax_number = Column('luxury_tax_number', String(15))
    gstin = Column('gstin', String(30), nullable=True)
    is_leased = Column('is_leased', Boolean, default=False)
    provider_hotel_code = Column(String(100), nullable=True)
    # No foreign key indexes as of now
    ext_id = Column('ext_id', ForeignKey('provider.id'), nullable=True)
    provider = relationship('Provider', backref=backref('property_detail'))
    # legal_signature: link/url, synonymous to legal_seal
    legal_signature = Column('legal_signature', String(500))

    navision_code = Column('navision_code', String(10), nullable=True)

    sold_as_id = Column(Integer, ForeignKey('param.id'), nullable=True)
    sold_as = relationship('Param', lazy="select")
    policies = relationship('PropertyPolicy', secondary='property_policy_map',
                            backref=backref('property_details', lazy='select'))
    is_housekeeping_enabled = Column(Boolean, default=False, server_default=expression.false(), nullable=False)
    has_lut = Column(Boolean, default=False, server_default=expression.false(), nullable=False)

    superhero_price_slab = Column(Enum(SuperHeroPriceSlabChoices), nullable=True)

    is_ds_pricing_enabled = Column(Boolean, default=False, server_default=expression.false(), nullable=False)

    churn_initiation_date = Column('churn_initiation_date', DateTime(timezone=True))

    is_partner_pricing_enabled = Column(Boolean, default=False, server_default=expression.false(), nullable=False)

    hygiene_shield_name = Column('hygiene_shield_name',
                                 Enum(HygieneShieldNameChoices, name='hygiene_shield_name_choices'), nullable=True)

    is_msme = Column(Boolean, default=False, server_default=expression.false())
    tan = Column('tan', String(10))
    is_hotel_superhero_setup_completed = Column('is_hotel_superhero_setup_completed', Boolean, default=False,
                                                server_default=expression.false(),
                                                nullable=False)
    msme_number = Column('msme_number', String)

    def __str__(self):
        return str.format('%s' % self.property_id)


class SystemProperty(Base, TimeStampMixin):
    __tablename__ = 'system_property'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(50), unique=True, nullable=False)
    value = Column('value', String(50))

    def __str__(self):
        return str.format('%s:%s' % (self.name, self.value))


class GoogleDriveBaseFolder(Base, TimeStampMixin):
    __tablename__ = 'google_drive_base_folder'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'))
    property = relationship('Property', backref=backref('google_drive_base_folder', uselist=False))
    folder_name = Column('folder_name', String(50), nullable=False, unique=True)
    folder_id = Column('folder_id', String(50), nullable=False)
    folder_link = Column('folder_link', String(500), nullable=False)
    property_documents_file_id = Column('property_documents_file_id', String(50))
    property_documents_link = Column('property_documents_link', String(500))
    property_images_file_id = Column('property_images_file_id', String(50))
    property_images_link = Column('property_images_link', String(500))

    def __str__(self):
        return str.format('%s Folder' % self.folder_name)


class GoogleDriveFile(Base, TimeStampMixin):
    __tablename__ = 'google_drive_file'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'))
    property = relationship('Property', backref='google_drive_files')
    file_id = Column('file_id', String(50), nullable=True)
    file_name = Column('file_name', String(100), nullable=True)
    file_type = Column('file_type', Enum(*GoogleDriveFileTypeChoices.FILE_TYPE_CHOICES, name='file_type_choices'),
                       nullable=False)

    def __str__(self):
        return str.format('%s' % self.file_name)


class Country(Base, TimeStampMixin):
    __tablename__ = 'country'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False, unique=True)
    iso_code = Column('iso_code', String(3), unique=True)

    def __str__(self):
        return str.format('%s' % self.name)


class Region(Base, TimeStampMixin):
    __tablename__ = 'region'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False, unique=True)

    def __str__(self):
        return str(self.name)


class Cluster(Base, TimeStampMixin):
    __tablename__ = 'cluster'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False, unique=True)
    region_id = Column('region_id', ForeignKey('region.id'),
                       nullable=True)  # Nullable due to need for backward compatibility
    region = relationship('Region', backref='clusters')

    def __str__(self):
        return str.format('%s' % self.name)


class State(Base, TimeStampMixin):
    __tablename__ = 'state'

    id = Column('id', Integer, primary_key=True)
    code = Column('code', Integer)
    name = Column('name', String(100), nullable=False)
    country_id = Column('country_id', ForeignKey('country.id'), nullable=False)
    country = relationship('Country', backref='states', innerjoin=True)

    __table_args__ = (UniqueConstraint('country_id', 'name', name='_unique_state_country'),)

    def __str__(self):
        return str.format('%s, %s' % (self.name, self.country_id))


class City(Base, TimeStampMixin):
    __tablename__ = 'city'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False)
    state_id = Column('state_id', ForeignKey('state.id'), nullable=False)
    state = relationship('State', backref='cities', innerjoin=True)
    cluster_id = Column('cluster_id', ForeignKey('cluster.id'))
    cluster = relationship('Cluster', backref='cities')
    latitude = Column('latitude', DECIMAL(precision=9, scale=6))
    longitude = Column('longitude', DECIMAL(precision=9, scale=6))

    __table_args__ = (UniqueConstraint('state_id', 'name', name='_unique_state_city'),)

    def __str__(self):
        return str.format('%s, %s' % (self.name, self.state_id))


class CityAlias(Base, TimeStampMixin):
    __tablename__ = 'city_alias'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False)
    city_id = Column('city_id', ForeignKey('city.id'), nullable=False)
    city = relationship('City', backref='aliases')

    __table_args__ = (UniqueConstraint('name', 'city_id', name='_unique_city_alias'),)

    def __str__(self):
        return str.format('%s, %s' % (self.name, self.city))


class MicroMarket(Base, TimeStampMixin):
    __tablename__ = 'micro_market'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False)
    city_id = Column('city_id', ForeignKey('city.id'), nullable=False)
    city = relationship('City', backref='micro_markets')

    def __str__(self):
        return str.format('%s, %s' % (self.name, self.city))


class Locality(Base, TimeStampMixin):
    __tablename__ = 'locality'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False)
    city_id = Column('city_id', ForeignKey('city.id'), nullable=False)
    city = relationship('City', backref='localities', innerjoin=True)
    micro_market_id = Column('micro_market_id', ForeignKey('micro_market.id'))
    micro_market = relationship('MicroMarket', backref='localities')
    latitude = Column('latitude', DECIMAL(precision=9, scale=6))
    longitude = Column('longitude', DECIMAL(precision=9, scale=6))

    __table_args__ = (UniqueConstraint('city_id', 'name', name='_unique_locality_city'),)

    def __str__(self):
        return str.format('%s, %s' % (self.name, self.city))


class Location(Base, TimeStampMixin):
    __tablename__ = 'location'

    id = Column('id', Integer, primary_key=True)
    # TODO: Should be unique=True?
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', back_populates='location')
    latitude = Column('latitude', DECIMAL(precision=9, scale=6))
    longitude = Column('longitude', DECIMAL(precision=9, scale=6))
    pincode = Column('pincode', String)
    postal_address = Column('postal_address', String(1000))
    maps_link = Column('maps_link', String(1000))
    micro_market_id = Column('micro_market_id', ForeignKey('micro_market.id'))
    micro_market = relationship('MicroMarket', backref='locations')
    locality_id = Column('locality_id', ForeignKey('locality.id'))
    locality = relationship('Locality', backref='locations')
    city_id = Column('city_id', ForeignKey('city.id'), nullable=False)
    city = relationship('City', backref='locations', foreign_keys=[city_id], innerjoin=True)
    legal_address = Column(Text)
    legal_city_id = Column('legal_city_id', ForeignKey('city.id'))
    legal_city = relationship('City', backref='location', foreign_keys=[legal_city_id])
    legal_pincode = Column('legal_pincode', String)

    def __str__(self):
        return str.format('%s Location' % self.property_id)


class RoomType(Base, TimeStampMixin):
    __tablename__ = 'room_type'

    id = Column('id', Integer, primary_key=True)
    code = Column('code', String, unique=True)
    type = Column('type', String, nullable=False, unique=True)
    unirate_room_type_code = Column('unirate_room_type_code', String, unique=True)
    crs_room_type_code = Column('crs_room_type_code', String, unique=True)
    bb_room_type_code = Column('bb_room_type_code', String, unique=True)

    def __str__(self):
        return str.format('%s' % self.type)


class NewRatePlan(Base, TimeStampMixin):
    __tablename__ = 'new_rate_plan'

    id = Column(Integer, primary_key=True)
    code = Column(String, nullable=False)
    name = Column(String, nullable=False)


class NewRatePlanConfig(Base, TimeStampMixin):
    __tablename__ = 'new_rate_plan_config'

    property_id = Column(ForeignKey('property.id'), nullable=False)
    rate_plan_id = Column(ForeignKey('new_rate_plan.id'), nullable=False)
    room_type_id = Column(ForeignKey('room_type.id'), nullable=False)

    room_type = relationship('RoomType')
    rate_plan = relationship('NewRatePlan')
    property = relationship('Property')

    __table_args__ = (
        PrimaryKeyConstraint('rate_plan_id', 'room_type_id', 'property_id',
                             name='_unique_property_room_type_rate_plan'),)


class RatePlanAddon(Base, TimeStampMixin):
    __tablename__ = 'rate_plan_addon'

    rate_plan_id = Column(ForeignKey('new_rate_plan.id'), nullable=False)
    addon_id = Column(ForeignKey('addon.id'), nullable=False)

    rate_plan = relationship('NewRatePlan')
    addon = relationship('Addon')
    __table_args__ = (PrimaryKeyConstraint('rate_plan_id', 'addon_id', name='_unique_rate_plan_addon'),)


class Addon(Base, TimeStampMixin):
    __tablename__ = 'addon'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    code = Column(String, nullable=False)


class RatePlan(Base, TimeStampMixin):
    """
    Assuming in the future there would be multiple rate plans for treebo
    hotels might not be the case with External providers, where a single
    rate plan is hooked with each provider, so field 'ext_id' isn't unique
    """
    __tablename__ = 'rate_plan'

    id = Column('id', Integer, primary_key=True)
    plan = Column('plan', String, nullable=False, unique=True)
    unirate_rate_plan_code = Column('unirate_rate_plan_code', String, unique=True)
    crs_rate_plan_code = Column('crs_rate_plan_code', String, unique=True)
    bb_rate_plan_code = Column('bb_rate_plan_code', String, unique=True)
    ext_id = Column('ext_id', ForeignKey('provider.id'), nullable=True)
    treebo_plan_id = Column(Integer, ForeignKey('rate_plan.id'), nullable=True)

    treebo_plan = relationship("RatePlan", remote_side=[id])

    def __str__(self):
        return str.format('%s' % self.plan)


class OTA(Base, TimeStampMixin):
    __tablename__ = 'ota'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String, nullable=False, unique=True)
    ota_code = Column('ota_code', String, nullable=False, unique=True)
    mm_ota_code = Column('mm_ota_code', String, nullable=False)
    unirate_ota_code = Column('unirate_ota_code', String, nullable=False, unique=True)
    rate_push_enabled = Column('rate_push_enabled', Boolean, nullable=False, default=True)
    promo_push_enabled = Column('promo_push_enabled', Boolean, nullable=False, default=True)
    inventory_push_enabled = Column('inventory_push_enabled', Boolean, nullable=False, default=True)
    promo_push_api = Column('promo_push_api', Boolean, nullable=False, default=False)
    promo_disable_api = Column('promo_disable_api', Boolean, nullable=False, default=False)
    rcs_push_complete = Column('rcs_push_complete', Boolean, nullable=False, default=False)
    rcs_callback_complete = Column('rcs_callback_complete', Boolean, nullable=False, default=False)
    unirate_push_complete = Column('unirate_push_complete', Boolean, nullable=False, default=False)
    rcs_push_time = Column('rcs_push_time', DateTime(timezone=True))
    rcs_callback_time = Column('rcs_callback_time', DateTime(timezone=True))
    unirate_push_time = Column('unirate_push_time', DateTime(timezone=True))

    def __str__(self):
        return str.format('%s' % self.name)


class OtaProperty(Base, TimeStampMixin):
    __tablename__ = 'ota_property'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    ota_id = Column('ota_id', ForeignKey('ota.id'), nullable=False)
    rate_push_complete = Column('rate_push_complete', Boolean, default=False)
    promo_push_complete = Column('promo_push_complete', Boolean, default=False)
    inventory_push_complete = Column('inventory_push_complete', Boolean, default=False)
    rcs_push_complete = Column('rcs_push_complete', Boolean, nullable=False, default=False)
    rcs_callback_complete = Column('rcs_callback_complete', Boolean, nullable=False, default=False)
    unirate_push_complete = Column('unirate_push_complete', Boolean, nullable=False, default=False)
    rcs_push_time = Column('rcs_push_time', DateTime(timezone=True))
    rcs_callback_time = Column('rcs_callback_time', DateTime(timezone=True))
    unirate_push_time = Column('unirate_push_time', DateTime(timezone=True))

    property = relationship('Property')
    ota = relationship('OTA')

    __table_args__ = (UniqueConstraint('property_id', 'ota_id', name='_unique_property_ota'),)

    def __str__(self):
        return str.format('%s - %s' % (self.property_id, self.ota_id))


class OtaPropertyMapping(Base):
    __tablename__ = 'ota_property_mapping'

    id = Column('id', Integer, primary_key=True)

    ota_property_id = Column('ota_property_id', ForeignKey('ota_property.id'), unique=True, nullable=False)
    ota_hotel_code = Column('ota_hotel_code', String, nullable=False)
    username = Column('username', String)
    access_token = Column('access_token', String)

    ota_property = relationship('OtaProperty', backref='hotel_mappings')

    def __str__(self):
        return str.format('%s' % self.ota_property)


class OtaRoomMapping(Base):
    __tablename__ = 'ota_room_mapping'

    id = Column('id', Integer, primary_key=True)

    ota_property_id = Column('ota_property_id', ForeignKey('ota_property.id'), nullable=False)
    room_type_id = Column('room_type_id', ForeignKey('room_type.id'), nullable=False)
    ota_room_code = Column('ota_room_code', String, nullable=False)
    ota_room_name = Column('ota_room_name', String)

    ota_property = relationship('OtaProperty', backref='room_mappings')
    room_type = relationship('RoomType')

    __table_args__ = (UniqueConstraint('ota_property_id', 'room_type_id', name='_unique_property_ota_room'),)

    def __str__(self):
        return str.format('%s' % self.ota_property)


class OtaRatePlanMappings(Base):
    __tablename__ = 'ota_rate_plan_mapping'

    id = Column('id', Integer, primary_key=True)

    ota_property_id = Column('ota_property_id', ForeignKey('ota_property.id'), nullable=False)
    room_type_id = Column('room_type', ForeignKey('room_type.id'), nullable=False)
    rate_plan_id = Column('rate_plan', ForeignKey('rate_plan.id'), nullable=False)
    ota_rate_plan_code = Column('ota_rate_plan_code', String, nullable=False)
    ota_rate_plan_name = Column('ota_rate_plan_name', String)
    rate_push_enabled = Column('rate_push_enabled', Boolean, default=False)

    ota_property = relationship('OtaProperty', backref='rate_plan_mappings')
    room_type = relationship('RoomType')
    rate_plan = relationship('RatePlan')

    def __str__(self):
        return str.format('%s' % self.ota_property)


class Room(Base, TimeStampMixin):
    __tablename__ = 'room'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', backref='rooms')
    room_number = Column('room_number', String(30), nullable=False)
    room_type_id = Column('room_type_id', ForeignKey('room_type.id'), nullable=False)
    room_type = relationship('RoomType', backref='rooms', innerjoin=True)
    building_number = Column('building_number', String)
    floor_number = Column('floor_number', Integer)
    size = Column('size', String(100))
    is_active = Column('is_active', Boolean, default=True)
    room_type_config_id = Column('room_type_config_id', ForeignKey('room_type_configuration.id'))
    room_type_config = relationship('RoomTypeConfiguration', backref='property_rooms')
    room_size = Column('room_size', DECIMAL)
    linked_room_identifier = Column(Integer)

    __table_args__ = (UniqueConstraint('property_id', 'room_number', name='_unique_property_room_number'),)

    def __str__(self):
        return str.format('%s, %s' % (self.room_number, self.property_id))


class RoomTypeConfiguration(Base, TimeStampMixin):
    __tablename__ = 'room_type_configuration'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', backref='room_type_configurations')
    room_type_id = Column('room_type_id', ForeignKey('room_type.id'), nullable=False)
    room_type = relationship('RoomType', backref='room_type_configurations', innerjoin=True)
    extra_bed = Column('extra_bed', Enum(*RoomTypeConfigurationChoices.EXTRA_BED_CHOICES, name='extra_bed_choices'))
    min_occupancy = Column('min_occupancy', Integer, default=1)
    max_occupancy = Column('max_occupancy', String(20))
    adults = Column('adults', Integer)
    children = Column('children', Integer)
    max_total = Column('max_total', Integer)
    mm_id = Column('mm_id', String(50), unique=True)
    min_room_size = Column('min_room_size', DECIMAL)
    # No foreign key indexes as of now
    ext_id = Column('ext_id', ForeignKey('provider.id'), nullable=True)
    provider = relationship('Provider', backref=backref('ext_room_type_configurations'))
    ext_room_code = Column('ext_room_code', String(100), nullable=True)
    ext_room_name = Column('ext_room_name', String(50))
    description = Column(Text, nullable=True)
    display_name = Column(String(50), nullable=True)
    ext_rate_plan_code = Column(String(100), nullable=True)
    ext_rate_plan_name = Column(String(100), nullable=True)

    __table_args__ = (UniqueConstraint('property_id', 'room_type_id', name='_unique_property_room_type'),)

    def __str__(self):
        return str.format('%s, %s' % (self.room_type, self.property_id))


class RatePlanConfiguration(Base, TimeStampMixin):
    __tablename__ = 'rate_plan_configuration'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', backref='rate_plan_configurations')
    rate_plan_id = Column('rate_plan_id', ForeignKey('rate_plan.id'), nullable=False)
    rate_plan = relationship('RatePlan', backref='rate_plan_configurations')

    __table_args__ = (UniqueConstraint('property_id', 'rate_plan_id', name='_unique_property_rate_plan'),)

    def __str__(self):
        return str.format('%s, %s' % (self.rate_plan, self.property_id))


class GuestFacingProcess(Base, TimeStampMixin):
    noon = dtime.time(12, 0, 0)
    six_am = dtime.time(6, 0, 0)
    three_pm = dtime.time(15, 0, 0)

    __tablename__ = 'guest_facing_process'

    id = Column('id', Integer, primary_key=True)
    # TODO: Should be unique=True?
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', back_populates='guest_facing_process')
    checkin_time = Column('checkin_time', Time, default=noon)
    checkout_time = Column('checkout_time', Time, default=noon)
    free_early_checkin = Column('free_early_checkin', Time, default=six_am)
    free_late_checkout = Column('free_late_checkout', Time, default=three_pm)
    early_checkin_fee = Column('early_checkin_fee', String(100), default=constants.FULL_DAY_CHARGE,
                               nullable=False)
    late_checkout_fee = Column('late_checkout_fee', String(100), default=constants.FULL_DAY_CHARGE,
                               nullable=False)
    switch_over_time = Column("switch_over_time", Time, default=six_am)
    checkin_grace_time = Column("checkin_grace_time", Integer, default=540)
    checkout_grace_time = Column("checkout_grace_time", Integer, default=360)
    system_freeze_time = Column("system_freeze_time", Time, default=three_pm)

    def __str__(self):
        return str.format('%s Guest Facing Process' % self.id)


class GuestType(Base, TimeStampMixin):
    __tablename__ = 'guest_type'

    id = Column('id', Integer, primary_key=True)
    type = Column('type', String(20), nullable=False, unique=True)
    properties = relationship('Property', secondary=guest_type_property, backref='guest_types')

    def __str__(self):
        return str.format('%s' % self.type)


class Owner(Base, TimeStampMixin):
    __tablename__ = 'owner'

    id = Column('id', Integer, primary_key=True)
    first_name = Column('first_name', String(50), nullable=False)
    middle_name = Column('middle_name', String(50))
    last_name = Column('last_name', String(50))
    gender = Column('gender', Enum(*OwnerChoices.CHOICES_GENDER, name='gender_choices'), default=None)
    email = Column('email', String(50))
    phone_number = Column('phone_number', String(50))
    date_of_birth = Column('date_of_birth', Date)
    occupation = Column('occupation', String(50))
    education = Column('education', String(50))
    ownerships = relationship('Ownership', back_populates="owner")

    def __str__(self):
        return str.format('%s, %s' % (self.first_name, self.email))


class AmenityPublicWashroom(Base, TimeStampMixin):
    __tablename__ = 'amenity_public_washroom'

    id = Column('id', Integer, primary_key=True)
    gender_segregated = Column('gender_segregated', Boolean, default=False)
    property_amenity = relationship('PropertyAmenity', back_populates='public_washroom', uselist=False)

    def __str__(self):
        return str.format('%s: Washroom' % self.id)


class AmenityElevator(Base, TimeStampMixin):
    __tablename__ = 'amenity_elevator'

    id = Column('id', Integer, primary_key=True)
    floors_accessible = Column('floors_accessible', String(100))
    property_amenity = relationship('PropertyAmenity', back_populates='elevator', uselist=False)

    def __str__(self):
        return str.format('%s: Elevator' % self.id)


class AmenityParking(Base, TimeStampMixin):
    __tablename__ = 'amenity_parking'

    id = Column('id', Integer, primary_key=True)
    location = Column('location', Enum(*ParkingChoices.LOCATION_CHOICES, name='parking_location_choices'))
    max_two_wheelers = Column('max_two_wheelers', Integer)
    max_four_wheelers = Column('max_four_wheelers', Integer)
    charges = Column('charges', String(500))
    property_amenity = relationship('PropertyAmenity', back_populates='parking', uselist=False)

    def __str__(self):
        return str.format('%s: Parking' % self.id)


class AmenityDisableFriendly(Base, TimeStampMixin):
    __tablename__ = 'amenity_disable_friendly'

    id = Column('id', Integer, primary_key=True)
    ramp_available = Column('ramp_available', Boolean, default=False)
    wheelchair_count = Column('wheelchair_count', Integer)
    disable_friendly_room_available = Column('disable_friendly_room_available', Boolean, default=False)
    disable_friendly_rooms = Column('disable_friendly_rooms', String(500))
    property_amenity = relationship('PropertyAmenity', back_populates='disable_friendly', uselist=False)

    def __str__(self):
        return str.format('%s: Disable Friendly Amenity' % self.id)


class AmenitySwimmingPool(Base, TimeStampMixin):
    __tablename__ = 'amenity_swimming_pool'

    id = Column('id', Integer, primary_key=True)
    location = Column('location', Enum(*SwimmingPoolChoices.LOCATION_CHOICES, name='pool_location_choices'))
    pool_size = Column('pool_size', String(100))
    open_time = Column('open_time', Time)
    close_time = Column('close_time', Time)
    active = Column('active', Boolean, default=True)
    property_amenity = relationship('PropertyAmenity', back_populates='swimming_pool', uselist=False)

    def __str__(self):
        return str.format('%s: Pool' % self.id)


class AmenityGym(Base, TimeStampMixin):
    __tablename__ = 'amenity_gym'

    id = Column('id', Integer, primary_key=True)
    open_time = Column('open_time', Time)
    close_time = Column('close_time', Time)
    equipments_available = Column('equipments_available', String(1000))
    active = Column('active', Boolean, default=True)
    property_amenity = relationship('PropertyAmenity', back_populates='gym', uselist=False)

    def __str__(self):
        return str.format('%s: Gym' % self.id)


class AmenitySpa(Base, TimeStampMixin):
    __tablename__ = 'amenity_spa'

    id = Column('id', Integer, primary_key=True)
    open_time = Column('open_time', Time)
    close_time = Column('close_time', Time)
    active = Column('active', Boolean, default=True)
    property_amenity = relationship('PropertyAmenity', back_populates='spa', uselist=False)

    def __str__(self):
        return str.format('%s: Spa' % self.id)


class AmenityLaundry(Base, TimeStampMixin):
    __tablename__ = 'amenity_laundry'

    id = Column('id', Integer, primary_key=True)
    pickup_time = Column('pickup_time', String(500))
    drop_time = Column('drop_time', String(500))
    is_external = Column('is_external', Boolean, default=True)
    property_amenity = relationship('PropertyAmenity', back_populates='laundry', uselist=False)

    def __str__(self):
        return str.format('%s: Laundry' % self.id)


class Cuisine(Base, TimeStampMixin):
    __tablename__ = 'cuisine'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(50), unique=True)

    def __str__(self):
        return str.format('%s' % self.name)


class AmenityBreakfast(Base, TimeStampMixin):
    __tablename__ = 'amenity_breakfast'

    id = Column('id', Integer, primary_key=True)
    type = Column('type', Enum(*BreakfastChoices.BREAKFAST_TYPE_CHOICES, name='breakfast_type_choices'))
    service_area = Column('service_area', Enum(*BreakfastChoices.SERVICE_AREA_CHOICES, name='service_area_choices'))
    non_veg = Column('non_veg', Boolean, default=False)
    rotational = Column('rotational', Boolean, default=False)
    cuisines = relationship('Cuisine', secondary=breakfast_cuisine, backref='breakfasts')
    property_amenity = relationship('PropertyAmenity', back_populates='breakfast', uselist=False)

    def __str__(self):
        return str.format('%s: Breakfast' % self.id)


class AmenityPayment(Base, TimeStampMixin):
    __tablename__ = 'amenity_payment'

    id = Column('id', Integer, primary_key=True)
    amex_accepted = Column('amex_accepted', Boolean, default=False)
    wallet_accepted = Column('wallet_accepted', Boolean, default=False)
    property_amenity = relationship('PropertyAmenity', back_populates='payment', uselist=False)

    def __str__(self):
        return str.format('%s: Payment' % self.id)


class AmenityPrivateCab(Base, TimeStampMixin):
    __tablename__ = 'amenity_private_cab'

    id = Column('id', Integer, primary_key=True)
    charges = Column('charges', String(500))
    property_amenity = relationship('PropertyAmenity', back_populates='private_cab', uselist=False)

    def __str__(self):
        return str.format('%s: Cab' % self.id)


class AmenityMixin(object):
    amenity_keys = []

    def get_applicable_amenities(self):
        applicable_amenities = set()

        for key in self.amenity_keys:
            value = key
            if type(key) is tuple:
                value = key[1]
                key = key[0]

            if getattr(self, key, False):
                applicable_amenities.add(value)

        return applicable_amenities


class PropertyAmenity(Base, TimeStampMixin, AmenityMixin):
    __tablename__ = 'property_amenity'

    id = Column('id', Integer, primary_key=True)

    # TODO: Should be unique=True?
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', backref=backref('property_amenity', uselist=False))

    # TODO: Should be unique=True, or is it one-to-many?
    public_washroom_id = Column('public_washroom_id', ForeignKey('amenity_public_washroom.id'))
    public_washroom = relationship('AmenityPublicWashroom', back_populates='property_amenity')

    # TODO: Should be unique=True, or is it one-to-many?
    elevator_id = Column('elevator_id', ForeignKey('amenity_elevator.id'))
    elevator = relationship('AmenityElevator', back_populates='property_amenity')

    lobby_ac = Column('lobby_ac', Boolean, default=False)
    lobby_furniture = Column('lobby_furniture', Boolean, default=False)
    lobby_smoke_alarm = Column('lobby_smoke_alarm', Boolean, default=False)
    security = Column('security', Boolean, default=False)
    pantry = Column('pantry', Boolean, default=False)
    cloak_room = Column('cloak_room', Boolean, default=False)
    travel_desk = Column('travel_desk', Boolean, default=False)
    room_service = Column('room_service', Boolean, default=False)
    roof_top_cafe = Column('roof_top_cafe', Boolean, default=False)
    pool_table = Column('pool_table', Boolean, default=False)
    pets_allowed = Column('pets_allowed', Boolean, default=False)

    # TODO: Should be unique=True, or is it one-to-many?
    parking_id = Column('parking_id', ForeignKey('amenity_parking.id'))
    parking = relationship('AmenityParking', back_populates='property_amenity')

    private_cab_id = Column('private_cab_id', ForeignKey('amenity_private_cab.id'))
    private_cab = relationship('AmenityPrivateCab', back_populates='property_amenity')

    iron_board_count = Column('iron_board_count', Integer, default=0)
    driver_quarters_count = Column('driver_quarters_count', Integer, default=0)

    disable_friendly_id = Column('disable_friendly_id', ForeignKey('amenity_disable_friendly.id'))
    disable_friendly = relationship('AmenityDisableFriendly', back_populates='property_amenity')

    swimming_pool_id = Column('swimming_pool_id', ForeignKey('amenity_swimming_pool.id'))
    swimming_pool = relationship('AmenitySwimmingPool', back_populates='property_amenity')

    gym_id = Column('gym_id', ForeignKey('amenity_gym.id'))
    gym = relationship('AmenityGym', back_populates='property_amenity')

    spa_id = Column('spa_id', ForeignKey('amenity_spa.id'))
    spa = relationship('AmenitySpa', back_populates='property_amenity')

    laundry_id = Column('laundry_id', ForeignKey('amenity_laundry.id'))
    laundry = relationship('AmenityLaundry', back_populates='property_amenity')

    breakfast_id = Column('breakfast_id', ForeignKey('amenity_breakfast.id'))
    breakfast = relationship('AmenityBreakfast', back_populates='property_amenity')

    payment_id = Column('payment_id', ForeignKey('amenity_payment.id'))
    payment = relationship('AmenityPayment', back_populates='property_amenity')

    def __str__(self):
        return str.format('%s Property Amenity' % self.property_id)

    amenity_keys = ['public_washroom', 'elevator', 'lobby_ac', 'lobby_furniture', 'lobby_smoke_alarm',
                    ('security', '24_hour_security'),
                    'pantry', ('cloak_room', 'luggage_storage'), 'travel_desk', 'room_service',
                    ('roof_top_cafe', 'roof_top_restaurant'),
                    'pool_table',
                    ('pets_allowed', 'pet_friendly'), 'parking', ('private_cab', 'cab_service'),
                    ('iron_board_count', 'ironing_board'),
                    ('driver_quarters_count', 'driver_quarter'), ('disable_friendly', 'wheel_chair'), 'swimming_pool',
                    'gym', 'spa', ('laundry', 'guest_laundry'), ('breakfast', 'free_breakfast'),
                    ('payment', 'card_payment_accepted')]


class Bar(Base, TimeStampMixin):
    __tablename__ = 'bar'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', backref='bars')
    name = Column('name', String(100))
    open_time = Column('open_time', Time)
    last_order_time = Column('last_order_time', Time)
    close_time = Column('close_time', Time)
    room_start_time = Column('room_start_time', Time)
    room_end_time = Column('room_end_time', Time)

    def __str__(self):
        return str.format('%s: %s-%s' % (self.id, self.name, self.property_id))


class Restaurant(Base, TimeStampMixin):
    __tablename__ = 'restaurant'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', backref='restaurants')
    name = Column('name', String(100))
    non_veg = Column('non_veg', Boolean, default=False)
    cuisines = relationship('Cuisine', secondary=restaurant_cuisine, backref='restaurants')
    open_time = Column('open_time', Time)
    last_order_time = Column('last_order_time', Time)
    close_time = Column('close_time', Time)
    a_la_carte = Column('a_la_carte', Boolean, default=False)
    buffet = Column('buffet', Boolean, default=False)
    outside_food_allowed = Column('outside_food_allowed', Boolean, default=False)
    baby_milk_served = Column('baby_milk_served', Boolean, default=False)
    baby_milk_timing = Column('baby_milk_timing', String(500))
    handwash_present = Column('handwash_present', Boolean, default=False)
    washroom_present = Column('washroom_present', Boolean, default=False)
    egg_served = Column('egg_served', Boolean, default=False)
    jain_food_served = Column('jain_food_served', Boolean, default=False)
    room_service_start_time = Column('room_service_start_time', Time)
    room_service_end_time = Column('room_service_end_time', Time)

    def __str__(self):
        return str.format('%s: %s-%s' % (self.id, self.name, self.property_id))


class BanquetHall(Base, TimeStampMixin):
    __tablename__ = 'banquet_hall'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', backref='banquet_halls')
    name = Column('name', String(100))
    floor = Column('floor', Integer)
    capacity = Column('capacity', Integer)
    size = Column('size', String(100))

    def __str__(self):
        return str.format('%s: %s' % (self.id, self.property_id))


class AmenityIntercom(Base, TimeStampMixin):
    __tablename__ = 'amenity_intercom'

    id = Column('id', Integer, primary_key=True)
    all_rooms_connected = Column('all_rooms_connected', Boolean, default=False)
    room_amenity = relationship('RoomAmenity', back_populates='intercom', uselist=False)

    def __str__(self):
        return str.format('%s: Intercom' % self.id)


class AmenityHotWater(Base, TimeStampMixin):
    __tablename__ = 'amenity_hot_water'

    id = Column('id', Integer, primary_key=True)
    central_geyser = Column('central_geyser', Boolean, default=False)
    room_geyser = Column('room_geyser', Boolean, default=False)
    from_time = Column('from_time', Time)
    to_time = Column('to_time', Time)
    room_amenity = relationship('RoomAmenity', back_populates='hot_water', uselist=False)

    def __str__(self):
        return str.format('%s: Hot Water' % self.id)


class AmenityTV(Base, TimeStampMixin):
    __tablename__ = 'amenity_tv'

    id = Column('id', Integer, primary_key=True)

    vendor = Column('vendor', String(100))
    tv_type = Column('tv_type', Enum(*TvChoices.TV_CHOICES, name='tv_choices'))
    connection_type = Column('connection_type', Enum(*TvChoices.CONNECTION_CHOICES, name='connection_choices'))
    size = Column('size', String(50))
    room_amenity = relationship('RoomAmenity', back_populates='tv', uselist=False)

    def __str__(self):
        return str.format('%s: TV' % self.id)


class AmenityAC(Base, TimeStampMixin):
    __tablename__ = 'amenity_ac'

    id = Column('id', Integer, primary_key=True)
    ac_type = Column('ac_type', Enum(*AcChoices.AC_CHOICES, name='ac_choices'))
    room_amenity = relationship('RoomAmenity', back_populates='ac', uselist=False)

    def __str__(self):
        return str.format('%s: AC' % self.id)


class AmenityStove(Base, TimeStampMixin):
    __tablename__ = 'amenity_stove'

    id = Column('id', Integer, primary_key=True)
    stove_type = Column('stove_type', Enum(*StoveChoices.STOVE_TYPE_CHOICES, name='stove_type_choices'))
    availability = Column('availability',
                          Enum(*StoveChoices.STOVE_AVAILABILITY_CHOICES, name='stove_availability_choices'))
    room_amenity = relationship('RoomAmenity', back_populates='stove', uselist=False)

    def __str__(self):
        return str.format('%s: Stove' % self.id)


class AmenityTwinBed(Base, TimeStampMixin):
    __tablename__ = 'amenity_twin_bed'

    id = Column('id', Integer, primary_key=True)
    joinable = Column('joinable', Boolean, default=False)
    room_amenity = relationship('RoomAmenity', back_populates='twin_bed', uselist=False)

    def __str__(self):
        return str.format('%s: Twin Bed' % self.id)


class AmenityHeater(Base, TimeStampMixin):
    __tablename__ = 'amenity_heater'

    id = Column('id', Integer, primary_key=True)
    availability = Column('availability', Enum(*RoomAmenityChoices.AVAILABILITY_CHOICES, name='heater_choices'))
    charges = Column('charges', String(500))
    room_amenity = relationship('RoomAmenity', back_populates='heater', uselist=False)

    def __str__(self):
        return str.format('%s: Heater' % self.id)


class RoomAmenity(Base, TimeStampMixin, AmenityMixin):
    __tablename__ = 'room_amenity'

    id = Column('id', Integer, primary_key=True)
    # TODO: Should be unique=True? In database there are multiple room_amenity rows for few rooms
    # We see warning: SAWarning: Multiple rows returned with uselist=False for lazily-loaded attribute 'Room.amenity'
    room_id = Column('room_id', ForeignKey('room.id'), nullable=False)
    room = relationship('Room', backref=backref('amenity', uselist=False))

    mini_fridge = Column('mini_fridge', Boolean, default=False)
    balcony = Column('balcony', Boolean, default=False)
    kitchenette = Column('kitchenette', Boolean, default=False)
    kitchenette_utensils = Column('kitchenette_utensils', Boolean, default=False)
    king_sized_beds = Column('king_sized_beds', Boolean, default=False)
    queen_sized_beds = Column('queen_sized_beds', Boolean, default=False)
    single_beds = Column('single_beds', Boolean, default=False)
    wardrobe = Column('wardrobe', Boolean, default=False)
    locker_available = Column('locker_available', Boolean, default=False)
    microwave = Column('microwave', Boolean, default=False)
    luggage_shelf = Column('luggage_shelf', Boolean, default=False)
    study_table_chair = Column('study_table_chair', Boolean, default=False)
    sofa_chair = Column('sofa_chair', Boolean, default=False)
    coffee_table = Column('coffee_table', Boolean, default=False)
    other_furniture = Column('other_furniture', Boolean, default=False)
    smoking_room = Column('smoking_room', Boolean, default=False)
    bath_tub = Column('bath_tub', Boolean, default=False)
    shower_curtain = Column('shower_curtain', Boolean, default=False)
    smoke_alarm = Column('smoke_alarm', Boolean, default=False)
    shower_cabinets = Column('shower_cabinets', Boolean, default=False)
    living_room = Column('living_room', Boolean, default=False)
    dining_table = Column('dining_table', Boolean, default=False)
    windows = Column('windows', Boolean, default=False)
    treebo_toiletries = Column('treebo_toiletries', Boolean, default=False)
    fan_type = Column('fan_type', Enum(*RoomAmenityChoices.FAN_CHOICES, name='fan_type_choices'))
    lock_type = Column('lock_type', Enum(*RoomAmenityChoices.LOCK_CHOICES, name='lock_type_choices'))
    bucket_mug = Column('bucket_mug', Enum(*RoomAmenityChoices.AVAILABILITY_CHOICES, name='bucket_choices'))
    mosquito_repellent = Column('mosquito_repellent',
                                Enum(*RoomAmenityChoices.AVAILABILITY_CHOICES, name='mosquito_repellent_choices'))

    heater_id = Column('heater_id', ForeignKey('amenity_heater.id'))
    heater = relationship('AmenityHeater', back_populates='room_amenity')

    twin_bed_id = Column('twin_bed_id', ForeignKey('amenity_twin_bed.id'))
    twin_bed = relationship('AmenityTwinBed', back_populates='room_amenity')

    intercom_id = Column('intercom_id', ForeignKey('amenity_intercom.id'))
    intercom = relationship('AmenityIntercom', back_populates='room_amenity')

    hot_water_id = Column('hot_water_id', ForeignKey('amenity_hot_water.id'))
    hot_water = relationship('AmenityHotWater', back_populates='room_amenity')

    tv_id = Column('tv_id', ForeignKey('amenity_tv.id'))
    tv = relationship('AmenityTV', back_populates='room_amenity')

    ac_id = Column('ac_id', ForeignKey('amenity_ac.id'))
    ac = relationship('AmenityAC', back_populates='room_amenity')

    stove_id = Column('stove_id', ForeignKey('amenity_stove.id'))
    stove = relationship('AmenityStove', back_populates='room_amenity')

    def __str__(self):
        return str.format('%s Room Amenity' % self.room_id)

    amenity_keys = [('mini_fridge', 'fridge'), 'balcony', 'kitchenette', 'kitchenette_utensils',
                    ('king_sized_beds', 'king_bed'),
                    ('queen_sized_beds', 'queen_bed'), 'single_beds', ('wardrobe', 'cupboards'),
                    ('locker_available', 'safety_locker'), 'microwave',
                    'luggage_shelf', 'study_table_chair', 'sofa_chair', 'coffee_table', 'other_furniture',
                    'smoking_room', 'bath_tub', 'shower_curtain', 'smoke_alarm', 'shower_cabinets', 'living_room',
                    'dining_table', 'windows', ('treebo_toiletries', 'complimentary_toiletries'), ('fan_type', 'fan'),
                    ('lock_type', 'lock'),
                    'bucket_mug', 'mosquito_repellent', 'heater', 'twin_bed', 'intercom', ('hot_water', 'geyser'),
                    ('tv', 'flat_screen_tv'),
                    ('ac', 'ac_room'),
                    'stove']


class Landmark(Base, TimeStampMixin):
    __tablename__ = 'landmark'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False)
    latitude = Column('latitude', DECIMAL(precision=9, scale=6))
    longitude = Column('longitude', DECIMAL(precision=9, scale=6))

    # TODO: Delete the below attributes after Dedup
    property_id = Column('property_id', ForeignKey('property.id'), nullable=True)
    property = relationship('Property', backref='landmarks')
    type = Column('type', String(50), nullable=True)
    distance_from_property = Column('distance_from_property', DECIMAL(precision=5, scale=2))
    property_direction = Column('property_direction', String(1000))
    hatchback_cab_fare = Column('hatchback_cab_fare', DECIMAL(precision=7, scale=2))
    sedan_cab_fare = Column('sedan_cab_fare', DECIMAL(precision=7, scale=2))

    def __str__(self):
        return str.format('%s: %s' % (self.id, self.name))


class PropertyLandmark(Base, TimeStampMixin):
    __tablename__ = 'property_landmark'

    id = Column('id', Integer, primary_key=True)
    landmark_id = Column('landmark_id', ForeignKey('landmark.id'), nullable=False)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    type = Column('type', String(50), nullable=False)
    distance_from_property = Column('distance_from_property', DECIMAL(precision=5, scale=2), nullable=False)
    property_direction = Column('property_direction', String(1000))
    hatchback_cab_fare = Column('hatchback_cab_fare', DECIMAL(precision=7, scale=2))
    sedan_cab_fare = Column('sedan_cab_fare', DECIMAL(precision=7, scale=2))

    property = relationship(Property, backref="property_landmarks", innerjoin=True)
    landmark = relationship(Landmark, backref="property_landmarks", innerjoin=True)

    __table_args__ = (UniqueConstraint('landmark_id', 'property_id', name='_unique_property_landmark'),)


class Description(Base, TimeStampMixin):
    __tablename__ = 'description'

    id = Column('id', Integer, primary_key=True)
    # TODO: Should be unique=True?
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', back_populates='description')
    property_description = Column('property_description', String(5000))
    acacia_description = Column('acacia_description', String(5000))
    oak_description = Column('oak_description', String(5000))
    maple_description = Column('maple_description', String(5000))
    mahogany_description = Column('mahogany_description', String(5000))
    trilight_one = Column('trilight_one', String(500))
    trilight_two = Column('trilight_two', String(500))
    trilight_three = Column('trilight_three', String(500))

    def __str__(self):
        return str.format('%s Description' % self.property_id)


class NeighbouringPlace(Base, TimeStampMixin):
    __tablename__ = 'neighbouring_place'

    id = Column('id', Integer, primary_key=True)
    # TODO: Should be unique=True?
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', back_populates='neighbouring_place')
    nearest_hospital = Column('nearest_hospital', String(1000))
    utility_shops = Column('utility_shops', String(1000))
    restaurants = Column('restaurants', String(1000))
    tourist_spots = Column('tourist_spots', String(1000))
    corporate_offices = Column('corporate_offices', String(1000))
    popular_malls = Column('popular_malls', String(1000))
    shopping_streets = Column('shopping_streets', String(1000))
    city_centre = Column('city_centre', String(1000))

    def __str__(self):
        return str.format('%s Neighbouring Places' % self.property_id)


class Ownership(Base, TimeStampMixin):
    __tablename__ = 'ownership'

    id = Column('id', Integer, primary_key=True)
    owner_id = Column('owner_id', ForeignKey('owner.id'), nullable=False)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    primary = Column('primary', Boolean, default=False)

    property = relationship(Property, backref="ownerships", innerjoin=True)
    owner = relationship(Owner, back_populates="ownerships", innerjoin=True)

    __table_args__ = (UniqueConstraint('owner_id', 'property_id', name='_unique_property_owner'),)


class Notification(Base, TimeStampMixin):
    __tablename__ = 'notification'

    id = Column('id', Integer, primary_key=True)
    type = Column('type', String(100), unique=True)
    receivers = Column('receivers', String(1000))


class TransportStation(Base, TimeStampMixin):
    __tablename__ = 'transport_station'

    id = Column('id', Integer, primary_key=True)
    type = Column('type', Enum(*TransportStationChoices.TRANSPORT_STATION_CHOICES, name='station_choices'),
                  nullable=False)
    name = Column('name', String(100), nullable=False, unique=True)
    latitude = Column('latitude', DECIMAL(precision=9, scale=6))
    longitude = Column('longitude', DECIMAL(precision=9, scale=6))

    def __str__(self):
        return str.format('%s' % self.name)


class TransportStationProperty(Base, TimeStampMixin):
    __tablename__ = 'transport_station_property'

    id = Column('id', Integer, primary_key=True)
    transport_station_id = Column('transport_station_id', ForeignKey('transport_station.id'), nullable=False)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    distance_from_property = Column('distance_from_property', DECIMAL(precision=5, scale=2))
    property_direction = Column('property_direction', String(1000))
    hatchback_cab_fare = Column('hatchback_cab_fare', DECIMAL(precision=7, scale=2))
    sedan_cab_fare = Column('sedan_cab_fare', DECIMAL(precision=7, scale=2))

    property = relationship(Property, backref="transport_station_assocs", innerjoin=True)
    transport_station = relationship(TransportStation, backref="transport_station_assocs", innerjoin=True)

    __table_args__ = (UniqueConstraint('property_id', 'transport_station_id', name='_unique_property_station'),)


class MigrationDetail(Base, TimeStampMixin):
    __tablename__ = 'migration_detail'

    id = Column('id', Integer, primary_key=True)
    migration_sheet_name = Column('migration_sheet_name', String(200), nullable=False)
    is_success = Column('is_success', Boolean, default=False)
    old_hotel = Column('old_hotel', Boolean, default=False)
    error_message = Column('error_message', String(1000))


class PropertyImage(Base, TimeStampMixin):
    __tablename__ = 'property_image'

    id = Column('id', Integer, primary_key=True)
    path = Column('path', Text, nullable=False, unique=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    room_type_config_id = Column('room_type_config_id', ForeignKey('room_type_configuration.id'))
    tag_description = Column('tag_description', Text, default='')
    sort_order = Column('sort_order', Integer, default=0)

    property = relationship(Property, backref='property_images')
    room_type_config = relationship(RoomTypeConfiguration, backref='property_images')

    def __str__(self):
        return self.path


class PropertyVideo(Base, TimeStampMixin):
    __tablename__ = 'property_video'

    id = Column('id', Integer, primary_key=True)
    video_url = Column('video_url', Text)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    tag_description = Column('tag_description', Text, default='')
    sort_order = Column('sort_order', Integer, default=0)
    youtube_video_url = Column(Text)

    property = relationship(Property, backref='property_videos')

    def __str__(self):
        return self.video_url or self.youtube_video_url


class AmenitySummary(Base, TimeStampMixin):
    __tablename__ = 'amenity_summary'

    id = Column('id', Integer, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=False)
    property = relationship('Property', back_populates='amenity_summary')
    summary = Column('summary', Text, nullable=False)


class FacilityCategory(Base, TimeStampMixin):
    __tablename__ = 'facility_category'

    id = Column('id', Integer, primary_key=True)
    category = Column('category', String(100), nullable=False, unique=True)

    def __str__(self):
        return self.category


class FacilityCategoryMapping(Base, TimeStampMixin):
    __tablename__ = 'facility_category_mapping'

    id = Column('id', Integer, primary_key=True)
    category_id = Column('category_id', ForeignKey('facility_category.id'), nullable=False)
    facility_name = Column('facility_name', String, nullable=False, unique=True)
    category = relationship('FacilityCategory', backref='facilities')


class SkuCategory(Base, TimeStampMixin):
    __tablename__ = 'sku_category'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False, unique=True)
    code = Column('code', String(100), unique=True, nullable=False)
    hsn_sac = Column('hsn_sac', String(100), nullable=True)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES,
                                   name='status'),
                    default=StandardStatusChoices.ACTIVE)
    seller_sku_categories = relationship('SellerSkuCategory')
    has_slab_based_taxation = Column('has_slab_based_taxation', Boolean(), default=False)

    def __str__(self):
        return str.format('%s' % self.name)


class Channel(Base, TimeStampMixin):
    __tablename__ = 'channel'

    id = Column('id', String(100), primary_key=True)
    name = Column('name', String(100), nullable=False, unique=True)
    description = Column('description', String(400), nullable=True)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES,
                                   name='channel_status'),
                    default=StandardStatusChoices.ACTIVE)
    sub_channels = relationship('SubChannel', backref='channel', cascade='all, delete-orphan')
    applications = relationship('Application', backref='channel', cascade='all, delete-orphan')
    policies = relationship('PricingPolicy', secondary='pricing_mapping',
                            backref=backref('channels', lazy="select"))
    priority = Column('priority', Integer)

    def __str__(self):
        return str.format('%s' % self.name)


class SubChannel(Base, TimeStampMixin):
    __tablename__ = 'sub_channel'

    id = Column('id', String(100), primary_key=True)
    name = Column('name', String(100), nullable=False, unique=True)
    description = Column('description', String(400), nullable=True)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES,
                                   name='sub_channel_status'),
                    default=StandardStatusChoices.ACTIVE)
    channel_id = Column('channel_id', ForeignKey('channel.id'), nullable=False)
    policies = relationship('PricingPolicy', secondary='pricing_mapping',
                            backref=backref('sub_channels', lazy="select"))
    priority = Column('priority', Integer)

    def __str__(self):
        return str.format('%s' % self.name)


class Application(Base, TimeStampMixin):
    __tablename__ = 'application'

    id = Column('id', String(100), primary_key=True)
    name = Column('name', String(100), nullable=False, unique=True)
    description = Column('description', String(400), nullable=True)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES,
                                   name='application_status'),
                    default=StandardStatusChoices.ACTIVE)
    channel_id = Column('channel_id', ForeignKey('channel.id'), nullable=False)

    def __str__(self):
        return str.format('%s' % self.name)


class Provider(Base, TimeStampMixin):
    __tablename__ = 'provider'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(100), nullable=False, unique=True)
    code = Column('code', String(100), unique=True, nullable=False)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES,
                                   name='provider_status'),
                    default=StandardStatusChoices.ACTIVE)
    rate_plan = relationship('RatePlan', backref='provider')
    room_mappings = relationship('ProviderRoomTypeMapping', backref='provider')
    brands = relationship('Brand', secondary='provider_brand', lazy='subquery')

    def __str__(self):
        return str.format('%s' % self.name)


class ProviderRoomTypeMapping(Base):
    """
    Assumptions:
    Multiple external room types can be mapped to single room type in cs
    """
    __tablename__ = 'provider_room_mapping'

    id = Column('id', Integer, primary_key=True)
    ext_id = Column('ext_id', ForeignKey('provider.id'), nullable=False)
    room_type_id = Column('room_type_id', ForeignKey('room_type.id'), nullable=False)
    ext_room_code = Column('ext_room_code', String(100), nullable=False)
    ext_room_name = Column('ext_room_name', String(50))
    ext_description = Column(Text, nullable=True)
    display_name = Column(String(50), nullable=True)

    room_type = relationship('RoomType')

    __table_args__ = (UniqueConstraint('ext_id', 'room_type_id', 'ext_room_code',
                                       name='_unique_external_room_mapping_key'),)

    def __str__(self):
        return str.format('%s-%s' % (self.ext_id, self.room_type))


class SkuBundle(Base, TimeStampMixin):
    """
    https://www.reddit.com/r/flask/comments/2o4ejl/af_flask_sqlalchemy_two_foreign_keys_referencing/
    """
    __tablename__ = 'sku_bundle'

    id = Column(Integer, primary_key=True)
    bundle_id = Column(Integer, ForeignKey('sku.id', ondelete='cascade'), index=True, nullable=False)
    sku_id = Column(Integer, ForeignKey('sku.id', ondelete='cascade'), index=True, nullable=False)
    count = Column(Integer, default=1)

    bundle = relationship('Sku', backref=backref('bundle', passive_deletes=True), foreign_keys=[bundle_id],
                          innerjoin=True)
    sku = relationship('Sku', backref=backref('sku', passive_deletes=True), foreign_keys=[sku_id], innerjoin=True)

    __table_args__ = (UniqueConstraint('bundle_id', 'sku_id', name='unique_bundle_index'),)

    def __str__(self):
        return str.format('%s-%s-%s' % (self.bundle.name, self.sku.name, self.count))

    def __repr__(self):
        return '<Bundled_sku {}>'.format(self.bundle.name + " " + self.sku.name + " " + str(self.count))


class Sku(Base, TimeStampMixin):
    __tablename__ = 'sku'

    id = Column('id', Integer, primary_key=True)
    code = Column('code', String(100), unique=True)
    name = Column('name', String(100), nullable=False)
    display_name = Column(String(100), nullable=True)
    is_modular = Column(Boolean(), default=False)
    tax_type = Column('tax_type', Enum(*TaxTypeChoices.TAX_CHOICES, name='tax_choice'), default=TaxTypeChoices.unit,
                      nullable=False)
    sku_type = Column('sku_type', Enum(*SkuTypeChoices.SKU_CHOICES, name='sku_choice'), default=SkuTypeChoices.sku,
                      nullable=False)
    hsn_sac = Column('hsn_sac', String(100), nullable=True)
    description = Column('description', Text, nullable=True)
    saleable = Column('saleable', Boolean(), default=False)
    default_list_price = Column(DECIMAL(precision=10, scale=2), default=0.00)
    default_sale_price = Column(DECIMAL(precision=10, scale=2), default=0.00)
    category_id = Column('category_id', ForeignKey('sku_category.id'), index=True, nullable=False)
    chargeable_per_occupant = Column('chargeable_per_occupant', Boolean(), default=False)
    identifier = Column(String(400), index=True)
    tag = Column(String(100), index=True)
    sku_count = Column(Integer, default=0)
    flat_count_for_creation = Column('flat_count_for_creation', Integer, default=0)
    bundle_rule_id = Column(ForeignKey('param.id'), index=True, nullable=True)
    offering = Column(JSON)
    frequency = Column(JSON)

    # secondary expects a table object and not a mapped class so SkuBundle will not work
    sku_s = relationship('Sku', secondary='sku_bundle',
                         primaryjoin=id == SkuBundle.bundle_id,
                         secondaryjoin=id == SkuBundle.sku_id)

    # Department architecture field (will be added via migration)
    seller_template_id = Column(Integer, ForeignKey('seller_template.id'), nullable=True)

    # https://docs.sqlalchemy.org/en/latest/orm/loading_relationships.html#subquery-eager-loading
    category = relationship('SkuCategory', lazy="joined", innerjoin=True)
    bundle_rule = relationship('Param', foreign_keys=[bundle_rule_id])
    seller_template = relationship("SellerTemplate", foreign_keys=[seller_template_id], backref="skus")
    is_active = Column('is_active', Boolean(), default=False)
    sku_details = Column('sku_details', JSON)
    is_property_inclusion = Column(Boolean(), default=False)
    tax_at_room_rate = Column(Boolean, default=False)
    is_default_sku_for_property_launch = Column(Boolean, default=False)

    def add_skus(self, items):
        """
        Creating SkuBundle will add it to the self.bundle object automatically by sqlalchemy,
        no need to append the created SkuBundle to the sku_bundles(backref)
        :param items:
        :return:
        """
        for sku, count in items:
            if not self.is_modular and sku.is_modular and sku not in self.sku_s:
                SkuBundle(bundle=self, sku=sku, count=count)
        self.sku_count = len(items)

    def remove_sku(self, sku):
        if sku in self.sku_s:
            self.sku_s.remove(sku)

    @staticmethod
    def fill_code(mapper, connection, target):
        connection.execute(mapper.mapped_table.update().values(code=target.id)
                           .where(mapper.mapped_table.c.id == target.id))

    def as_dict(self):
        return dict(id=self.id, code=self.code, name=self.name, display_name=self.display_name,
                    is_modular=self.is_modular, tax_type=self.tax_type, sku_type=self.sku_type,
                    hsn_sac=self.hsn_sac, saleable=self.saleable, default_list_price=self.default_list_price,
                    default_sale_price=self.default_sale_price, category=self.category.code, tag=self.tag,
                    sku_count=self.sku_count, offering=self.offering, frequency=self.frequency)

    # admin_view uses __str__() first and then falls back to __repr__(), if not found
    def __str__(self):
        return str.format('%s (%s)' % (self.name, self.code))

    def __repr__(self):
        return '<Sku {}>'.format(str(self.id) + " " + self.name)


class PropertySku(Base, TimeStampMixin):
    """
    For passive_deletes=True, please refer
    https://stackoverflow.com/a/38770040/1790760

    """
    __tablename__ = 'property_sku'

    id = Column('id', Integer, primary_key=True)
    sku_id = Column('sku_id', ForeignKey('sku.id'), index=True, nullable=False)
    property_id = Column('property_id', ForeignKey('property.id'), index=True, nullable=False)
    seller_id = Column('seller_id', ForeignKey('seller.seller_id'), nullable=True, unique=True)  # SKU ownership
    display_name = Column(String(100), nullable=True)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES,
                                   name='property_sku_status'),
                    default=StandardStatusChoices.INACTIVE)
    saleable = Column('saleable', Boolean(), default=False)
    rack_rate = Column(NUMERIC(precision=10, scale=2), nullable=True)

    property = relationship(Property, backref=backref('property_skus'), innerjoin=True)
    sku = relationship(Sku, backref=backref('property_skus'), innerjoin=True)
    seller = relationship("Seller", backref="property_skus")  # SKU ownership relationship
    description = Column(String, nullable=True)
    extra_information = Column('extra_information', JSON, nullable=True)
    sell_separate = Column('sell_separate', Boolean(), default=True)

    __table_args__ = (UniqueConstraint('sku_id', 'property_id', name='_unique_property_sku'),)

    def __init__(self, *args, **kwargs):
        db_engine.get_session().query(Param).all()
        super().__init__(*args, **kwargs)

    @python_property
    def activation_callbacks_list(self):
        sku_acks = db_engine.get_session().query(SkuActivation).filter(SkuActivation.sku_id == self.sku_id,
                                                                       SkuActivation.property_id == self.property_id)
        return [sku_ack.param.value for sku_ack in sku_acks]


class Param(Base):
    """
    Entity-attribute-value model
    Parameter enum table to avoid using enums, it helps provide the following

    Referential integrity
    Value to attribute mapping
    Prevention of invalid values.
    """
    __tablename__ = 'param'

    id = Column('id', Integer, primary_key=True)
    # table
    entity = Column('entity', String(100), nullable=False)
    # table field can and cannot be mapped to table field, if mapped create a FK reference in table for field
    field = Column('field', String(100), nullable=False)
    # value field
    value = Column('value', Text, nullable=False)
    # definition
    definition = Column('definition', Text, nullable=True, default='0')
    # is validation needed for this field
    validate = Column('validate', Boolean(), default=False)

    __table_args__ = (UniqueConstraint('entity', 'field', 'value', 'validate', name='unique_entity_param_index'),)

    def __str__(self):
        return str.format('%s' % self.value)

    def __repr__(self):
        return '<Param {}>'.format(str(self.entity) + " " + self.field)


class SkuAttribute(Base):
    """
    Sku attributes
    """
    __tablename__ = 'sku_attribute'

    id = Column('id', Integer, primary_key=True)
    sku_id = Column(Integer, ForeignKey('sku.id', ondelete='cascade'), nullable=False)
    # table field
    key = Column('key', String(100), nullable=False)
    # value field
    value = Column('value', String(200), nullable=False)

    sku = relationship('Sku', backref=backref('sku_attributes', passive_deletes=True))

    def __str__(self):
        return str.format('%s-%s-%s' % (self.sku, self.key, self.value))

    def __repr__(self):
        return '<SkuAttribute {}>'.format(str(self.sku) + " " + str(self.key) + " " + self.value)


class SkuActivation(Base, TimeStampMixin):
    """
    A property when deleted the corresponding records can be deleted, as they will be limited, but when sku is deleted
    the records will be as many as the properties, so deleting them is not recommended, no cascade delete on Sku
    """
    __tablename__ = 'sku_activation'

    id = Column(Integer, primary_key=True)
    property_id = Column(ForeignKey('property.id', ondelete='cascade'), nullable=False)
    sku_id = Column(Integer, ForeignKey('sku.id'), nullable=False)
    service_id = Column(Integer, ForeignKey('param.id'), nullable=False)

    property = relationship(Property, backref=backref('property_sku_activation'))
    sku = relationship(Sku, backref=backref('sku_activation'))
    param = relationship(Param, backref=backref('param_sku_activation'))

    def __str__(self):
        return str.format('%s-%s-%s' % (self.property_id, self.sku.name, self.param.value))

    def __repr__(self):
        return '<StockActivation {}>'.format(self.property_id + " " + self.sku.name + " " + self.param.value)

    __table_args__ = (UniqueConstraint('property_id', 'sku_id', 'service_id',
                                       name='_unique_sku_activation_service_key'),)


class PricingPolicy(Base, TimeStampMixin):
    __tablename__ = 'pricing_policy'

    id = Column(Integer, primary_key=True)
    code = Column(String(100), unique=True, nullable=False)
    name = Column(String(100), unique=True, nullable=False)
    is_default = Column(Boolean(), default=False)
    display_name = Column(String(100), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    status = Column(Enum(*StandardStatusChoices.STATUSES,
                         name='pricing_policy_status'),
                    default=StandardStatusChoices.ACTIVE)

    def __str__(self):
        return str.format('%s' % self.name)

    def __repr__(self):
        return '<PricingPolicy {}>'.format(self.name + " " + self.code)


class PricingMapping(Base):
    __tablename__ = 'pricing_mapping'

    id = Column(Integer, primary_key=True)
    pricing_id = Column('pricing_policy_id', ForeignKey('pricing_policy.id'), nullable=False)
    channel_id = Column('channel_id', ForeignKey('channel.id'), nullable=False)
    sub_channel_id = Column('sub_channel_id', ForeignKey('sub_channel.id'), nullable=False)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES, name='pricing_mapping_status'),
                    default=StandardStatusChoices.ACTIVE)

    __table_args__ = (UniqueConstraint('pricing_policy_id', 'channel_id', 'sub_channel_id',
                                       name='_unique_pricing_policy_mapping_key'),)

    pricing = relationship(PricingPolicy, backref=backref('pricing_mapping'))

    channel = relationship(Channel, backref=backref('pricing_mapping'), lazy='select')
    sub_channel = relationship(SubChannel, backref=backref('pricing_mapping'), lazy='select')

    def __str__(self):
        return str.format('%s' % self.pricing)

    def __repr__(self):
        return '<PricingMapping {}>'.format(self.channel_id + " " + self.sub_channel_id + " " + self.status)


class Brand(Base, TimeStampMixin):
    __tablename__ = 'brand'

    id = Column('id', Integer, primary_key=True)
    code = Column('code', String(100), unique=True, nullable=False)
    name = Column(String(100), nullable=False, unique=True)
    display_name = Column(String(100), nullable=False, unique=True)
    legal_name = Column(String(100), nullable=False, unique=True)
    short_description = Column(String(400), nullable=True)
    long_description = Column(Text, nullable=True)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES, name='brand_status'),
                    default=StandardStatusChoices.ACTIVE)
    logo = Column('logo_path', Text, nullable=False, unique=True)
    color = Column(String(20))
    brand_code = Column('brand_code', Text, nullable=True)

    def __str__(self):
        return str.format('%s' % self.name)


class ProviderBrandMapping(Base, TimeStampMixin):
    __tablename__ = 'provider_brand'

    id = Column('id', Integer, primary_key=True)
    provider_id = Column(ForeignKey('provider.id'), nullable=False)
    brand_id = Column(ForeignKey('brand.id'), nullable=False)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES, name='provider_brand_status'),
                    default=StandardStatusChoices.ACTIVE)

    provider = relationship('Provider')
    brand = relationship('Brand')

    def __str__(self):
        return str.format('%s-%s' % (self.provider.name, self.brand.name))


class PropertyBrandMapping(Base, TimeStampMixin):
    __tablename__ = 'property_brand'

    id = Column('id', Integer, primary_key=True)
    property_id = Column(ForeignKey('property.id'), nullable=False)
    brand_id = Column(ForeignKey('brand.id'), nullable=False)
    status = Column('status', Enum(*StandardStatusChoices.STATUSES, name='property_brand_status'),
                    default=StandardStatusChoices.ACTIVE)

    property = relationship('Property')
    brand = relationship('Brand')

    def __str__(self):
        return str.format('%s-%s' % (self.property_id, self.brand_id))


class RuptubLegalEntityDetails(Base):
    __tablename__ = 'ruptub_legal_entities'

    id = Column('id', Integer, primary_key=True)
    state_id = Column('state_id', ForeignKey('state.id'), unique=True, nullable=False)
    gstin = Column('gstin', String(15), unique=True, nullable=False)
    date_of_registration = Column('date_of_registration', Date, nullable=False)
    address_line_1 = Column('address_line_1', String(100), nullable=False)
    address_line_2 = Column('address_line_2', String(100), nullable=False)
    address_city = Column('address_city', String(100), nullable=False)
    address_pincode = Column('address_pincode', String(6), nullable=False)
    legal_name = Column('legal_name', String(32), default="Ruptub Solutions Private Limited", nullable=False)

    state = relationship("State")

    def __str__(self):
        return str.format('%s-%s' % (self.state_id, self.gstin))


class PropertyPolicy(Base, TimeStampMixin, Policy):
    __tablename__ = 'property_policy'

    def __str__(self):
        return str.format('{id}-{policy_type}'.format(id=self.id, policy_type=self.policy_type))


class GlobalPolicy(Base, TimeStampMixin, Policy):
    __tablename__ = 'global_policy'

    def __str__(self):
        return str.format('{id}-{policy_type}'.format(id=self.id, policy_type=self.policy_type))


class SellerCategory(Base, TimeStampMixin):
    __tablename__ = 'seller_category'

    id = Column('id', Integer, primary_key=True)
    name = Column('name', String(50), nullable=False, unique=True)
    code = Column('code', String(20), unique=True, nullable=False)
    is_active = Column('is_active', Boolean, default=True)

    def __str__(self):
        return str(self.name)


class Seller(Base, TimeStampMixin):
    __tablename__ = 'seller'

    id = Column(Integer, primary_key=True)
    seller_id = Column(String, unique=True)
    name = Column(String(100), nullable=False)
    property_id = Column(String, ForeignKey('property.id'))
    seller_category_id = Column(Integer, ForeignKey('seller_category.id'), nullable=False)
    seller_config = Column(JSONB)

    city_id = Column(Integer, ForeignKey('city.id'), nullable=False)
    legal_city_id = Column(Integer, ForeignKey('city.id'))
    gstin = Column(String(20))
    legal_name = Column(String(100))
    legal_address = Column(Text)
    pincode = Column(String(20))
    legal_signature = Column(String(500))
    legal_pincode = Column(String(20))
    phone_number = Column(String(30))
    status = Column(String(50), nullable=False)
    base_currency_code = Column(String)
    timezone = Column(String)

    property = relationship('Property', foreign_keys=[property_id], lazy='joined')
    city = relationship('City', foreign_keys=[city_id], lazy='joined')
    legal_city = relationship('City', foreign_keys=[legal_city_id], lazy='joined')
    seller_category = relationship('SellerCategory')
    current_business_date = Column('current_business_date', Date)

    # Department and Terminal Type relationships (will be added via migration)
    default_department_id = Column(Integer, ForeignKey('property_department.id'), nullable=True)
    property_terminal_id = Column(Integer, ForeignKey('seller_template.id'), nullable=True)  # Terminal type
    is_auto_created = Column(Boolean, default=False)

    default_department = relationship("PropertyDepartment", back_populates="sellers")
    terminal_type = relationship("SellerTemplate", back_populates="sellers")

    def __str__(self):
        return str.format('%s: %s' % (self.seller_id, self.name))


class MenuCategory(Base, TimeStampMixin):
    __tablename__ = 'pos_menu_category'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    color = Column(String)
    icon = Column(String)
    seller_category_id = Column(Integer, ForeignKey('seller_category.id'), nullable=False)

    seller_category = relationship('SellerCategory')

    def __str__(self):
        return str.format('%s: %s' % (self.id, self.name))


class SellerSku(Base, TimeStampMixin):
    __tablename__ = 'seller_sku'

    id = Column(Integer, primary_key=True)
    sku_id = Column(Integer, ForeignKey('sku.id'), index=True, nullable=False)
    seller_id = Column(String, ForeignKey('seller.seller_id'), index=True, nullable=False)
    sku_category_code = Column(String(30), nullable=False)
    name = Column(String(100), nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    is_sellable = Column(Boolean(), default=False)
    menu_category_id = Column(Integer, ForeignKey('pos_menu_category.id'))
    pretax_price = Column(DECIMAL(precision=10, scale=2), default=0.00)
    icon = Column(Text)

    sku = relationship('Sku')
    seller = relationship('Seller')

    def __str__(self):
        return self.name


class RestaurantArea(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "restaurant_area"

    id = Column(Integer, autoincrement=True, primary_key=True)
    seller_id = Column(String, ForeignKey('seller.seller_id'), nullable=False)
    name = Column(String, nullable=False)
    display_order = Column(Integer, nullable=True)

    tables = relationship('RestaurantTable',
                          primaryjoin="and_(RestaurantArea.id==RestaurantTable.area_id, "
                                      "RestaurantTable.is_deleted==False)",
                          back_populates="restaurant_area")

    def __str__(self):
        return str.format('%s: %s: %s' % (self.id, self.name, self.seller_id))

    def __repr__(self):
        return '<RestaurantArea {}>'.format(str(self.id) + " " + self.name)


class RestaurantTable(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = 'restaurant_table'

    id = Column(Integer, autoincrement=True, primary_key=True)
    area_id = Column(Integer, ForeignKey('restaurant_area.id'), nullable=True)
    seller_id = Column(String, ForeignKey('seller.seller_id'), nullable=False)
    name = Column(String, nullable=True)
    table_number = Column(String, nullable=True)
    current_status = Column(String, nullable=True)
    status_updated_at = Column(DateTime(timezone=True))
    x_coordinate = Column(Integer, nullable=True)
    y_coordinate = Column(Integer, nullable=True)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)

    seats = relationship('TableSeat', primaryjoin="RestaurantTable.id==TableSeat.table_id",
                         back_populates="restaurant_table")
    restaurant_area = relationship("RestaurantArea")
    seller = relationship('Seller')

    def __str__(self):
        return str.format('%s: %s' % (self.table_number, self.current_status))

    def __repr__(self):
        return '<RestaurantTable {}>'.format(self.table_number)


class TableSeat(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "table_seat"

    id = Column(Integer, autoincrement=True, primary_key=True)
    seat_number = Column(Integer, nullable=True)
    table_id = Column(Integer, ForeignKey('restaurant_table.id'), index=True, nullable=False)
    restaurant_table = relationship('RestaurantTable')


class CurrencyConversionRateModel(Base, TimeStampMixin):
    __tablename__ = 'currency_conversion_rate'

    currency_conversion_rate_id = Column(Integer, autoincrement=True, primary_key=True)
    from_currency = Column(String, nullable=False)
    to_currency = Column(String, nullable=False)
    conversion_rate = Column(DECIMAL(precision=15, scale=4), nullable=False)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    property_id = Column(String, ForeignKey('property.id'), nullable=False)

    property = relationship('Property', foreign_keys=[property_id])

    def __str__(self):
        return "(from_currency: {code})(conversion_rate:{conv_rate})" \
               "(expiry_date:{expiry_date} (to_currency:{to_currency})".format(
                   code=self.from_currency, conv_rate=self.conversion_rate, expiry_date=self.expiry_date,
                   to_currency=self.to_currency)

    def validate_date(self):
        if not self.start_date <= self.end_date:
            raise Exception("End date should always be greater than start date")


class AvailableConfigModel(Base):
    __tablename__ = 'available_config'

    name = Column(String, primary_key=True)
    value_type = Column(String)
    description = Column(String)

    def __str__(self):
        return "{0} (type: {1})".format(self.name, self.value_type)


class AllowedConfigValueModel(Base):
    __tablename__ = 'allowed_config_value'

    id = Column(Integer, autoincrement=True, primary_key=True)
    config_name = Column(String, ForeignKey('available_config.name', ondelete='CASCADE'))
    value = Column(String)

    config = relationship('AvailableConfigModel', foreign_keys=[config_name],
                          backref=backref('allowed_config_values', passive_deletes='all'))


class TenantConfigModel(Base, TimeStampMixin):
    __tablename__ = 'tenant_config'

    tenant_config_id = Column(Integer, primary_key=True, autoincrement=True)
    config_name = Column(String, ForeignKey('available_config.name'))
    config_value = Column(String)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=True)
    active = Column(Boolean(), default=False)
    seller_id = Column('seller_id', ForeignKey('seller.seller_id'), nullable=True)

    config = relationship('AvailableConfigModel', foreign_keys=[config_name])
    property = relationship('Property', foreign_keys=[property_id])
    seller = relationship('Seller', foreign_keys=[seller_id])

    __table_args__ = (UniqueConstraint('config_name', 'property_id', 'seller_id',
                                       name='tenant_config_config_name_property_id_seller_id_key'),
                      Index('_unique_config_name_with_property_null_seller_null', config_name,
                            unique=True,
                            postgresql_where=and_(property_id.is_(None), seller_id.is_(None))),
                      Index('_unique_config_name_with_property_not_null_seller_null', config_name, property_id,
                            unique=True,
                            postgresql_where=and_(property_id.isnot(None), seller_id.is_(None))),
                      Index('_unique_config_name_with_seller_not_null_and_property_null', config_name, seller_id,
                            unique=True,
                            postgresql_where=and_(seller_id.isnot(None), property_id.is_(None))),
                      )

    def validate_config_value(self):
        try:
            PARSER = {
                ConfigValueType.INTEGER: IntegerParser(),
                ConfigValueType.STRING: StringParser(),
                ConfigValueType.JSON: JsonParser(),
                ConfigValueType.BOOLEAN: BooleanParser(),
                ConfigValueType.ARRAY: ListParser(),
                ConfigValueType.DATE: DateParser()
            }
            config_value = PARSER.get(ConfigValueType(self.config.value_type)).parse(self.config_value)
        except Exception:
            raise Exception

        if self.config.allowed_config_values:
            allowed_config_values = {allowed_value.value for allowed_value in self.config.allowed_config_values}

            if self.config.value_type == ConfigValueType.STRING.value:
                if config_value not in allowed_config_values:
                    from wtforms import validators
                    raise validators.ValidationError(
                        "Invalid values selected for config. Allowed values: {0}".format(allowed_config_values))
            else:
                invalid_values = [val for val in config_value if val not in allowed_config_values]
                if invalid_values:
                    from wtforms import validators
                    raise validators.ValidationError(
                        "Invalid values selected for config. Allowed values: {0}".format(allowed_config_values))


class UserDefinedEnumModel(Base):
    __tablename__ = 'user_defined_enum'

    enum_id = Column(Integer, autoincrement=True, primary_key=True)
    property_id = Column('property_id', ForeignKey('property.id'), nullable=True)
    enum_name = Column(String)
    label = Column(String)
    role = Column(String)

    property = relationship('Property', foreign_keys=[property_id])

    __table_args__ = (
        UniqueConstraint('property_id', 'enum_name', name='_unique_enum_name_per_property'),
    )


class EnumValuesModel(Base):
    __tablename__ = 'user_defined_enum_values'

    enum_value_id = Column(Integer, autoincrement=True, primary_key=True)
    enum_id = Column(Integer, ForeignKey('user_defined_enum.enum_id', ondelete='CASCADE'))
    value = Column(String)
    label = Column(String)

    enum = relationship('UserDefinedEnumModel', foreign_keys=[enum_id],
                        backref=backref('enum_values', lazy='joined', passive_deletes='all'))

    __table_args__ = (
        UniqueConstraint('enum_id', 'value', name='_unique_enum_value_per_enum'),
    )


class RestaurantMenuCategory(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = 'menu_category'

    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column('name', String, nullable=False)
    display_order = Column('display_order', Integer, nullable=False)

    menu_id = Column(Integer, ForeignKey('menu.id'), nullable=False)
    menu_item_categories = relationship('MenuItemCategory', back_populates="menu_category")
    menu_combo_categories = relationship('MenuComboCategory', back_populates="menu_category")

    def __str__(self):
        return str.format('%s' % (self.id))

    def __repr__(self):
        return '<Menu_Category {}>'.format(self.id)


class MenuTiming(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = 'menu_timing'

    id = Column(Integer, autoincrement=True, primary_key=True)
    days = Column('days', BYTEA, nullable=False)
    start_time = Column('start_time', Time(timezone=False), nullable=False)
    end_time = Column('end_time', Time(timezone=False), nullable=False)

    menu_id = Column(Integer, ForeignKey('menu.id'), nullable=False)

    def __str__(self):
        return str.format('%s' % (self.id))

    def __repr__(self):
        return '<Menu_Timing {}>'.format(self.id)


class MenuItem(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "menu_item"

    id = Column(Integer, autoincrement=True, primary_key=True)
    menu_id = Column(Integer, ForeignKey('menu.id'), nullable=False)
    item_id = Column(Integer, ForeignKey('item.id'), nullable=False)
    item_variant_id = Column(Integer, ForeignKey('item_variant.id'), nullable=True)
    sold_out = Column(Boolean, nullable=False)
    display_order = Column(Integer, nullable=False)
    item = relationship('Item', back_populates='menu_items')
    menu = relationship('Menu', back_populates='menu_items')
    item_variant = relationship('ItemVariant', back_populates='menu_item_variants')
    menu_item_categories = relationship(
        'MenuItemCategory',
        primaryjoin="and_(MenuItem.id==MenuItemCategory.menu_item_id, MenuItemCategory.is_deleted==False)",
        back_populates="menu_item")
    menu_item_item_variants = relationship(
        'MenuItemItemVariant',
        primaryjoin="and_(MenuItem.id==MenuItemItemVariant.menu_item_id, MenuItemItemVariant.is_deleted==False)",
        back_populates="menu_item")
    menu_item_side_items = relationship(
        'MenuItemSideItem',
        primaryjoin="and_(MenuItem.id==MenuItemSideItem.menu_item_id, MenuItemSideItem.is_deleted==False)",
        back_populates="menu_item")
    menu_item_item_customisations = relationship(
        'MenuItemItemCustomisation',
        primaryjoin="and_(MenuItem.id==MenuItemItemCustomisation.menu_item_id, "
                    "MenuItemItemCustomisation.is_deleted==False)",
        back_populates="menu_item")


class Item(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "item"

    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String, nullable=False)
    code = Column(String, nullable=True)
    description = Column(String, nullable=True)
    sku_category_code = Column(String, nullable=True)
    display_name = Column(String, nullable=True)
    print_name = Column(String, nullable=True)
    sold_out = Column(Boolean, nullable=False, default=False)
    prep_time = Column(Interval, nullable=False)
    use_as_side = Column(Boolean, nullable=False)
    contains_alcohol = Column(Boolean, nullable=False)
    pre_tax_price = Column(DECIMAL, nullable=True)
    cost = Column(DECIMAL, nullable=True)
    sku_id = Column(Integer, nullable=True)
    active = Column(Boolean, default=True)
    food_type = Column(String, nullable=False, default=FoodTypeChoices.VEGETARIAN.value)
    image = Column(String, nullable=True)
    allergen_info = Column(String, nullable=True)
    calorie_info = Column(String, nullable=True)
    seller_id = Column(String, ForeignKey('seller.seller_id'), nullable=False)
    kitchen_id = Column(Integer, ForeignKey('kitchen.id'), nullable=True)
    seller = relationship('Seller')
    item_variants = relationship(
        'ItemVariant', primaryjoin="and_(Item.id==ItemVariant.item_id, ItemVariant.is_deleted==False)",
        back_populates="item")
    side_items = relationship('SideItem', primaryjoin="and_(Item.id==SideItem.item_id, SideItem.is_deleted==False)",
                              foreign_keys="[SideItem.item_id]", back_populates="item")
    side_item_of = relationship("SideItem", foreign_keys="[SideItem.side_item_id]", back_populates="side_item")
    menu_items = relationship('MenuItem', back_populates="item")
    combo_items = relationship('ComboItem', back_populates="item")
    item_customisations = relationship(
        "ItemCustomisation",
        primaryjoin="and_(Item.id==ItemCustomisation.item_id, ItemCustomisation.is_deleted==False)",
        back_populates="item")


class ItemVariant(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "item_variant"

    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String, nullable=False)
    display_order = Column(Integer, nullable=False)
    pre_tax_price = Column(DECIMAL, nullable=False)
    cost = Column(DECIMAL, nullable=False)
    sku_id = Column(Integer, nullable=False)
    sku_category_code = Column(String, nullable=False)
    item_id = Column(Integer, ForeignKey('item.id'), nullable=False)
    item = relationship(
        'Item', primaryjoin="and_(ItemVariant.item_id==Item.id, Item.is_deleted==False)",
        back_populates='item_variants')
    menu_item_variants = relationship("MenuItem", back_populates="item_variant")
    menu_item_item_variants = relationship('MenuItemItemVariant', back_populates="item_variant")
    item_customisations = relationship(
        "ItemCustomisation",
        primaryjoin="and_(ItemVariant.id==ItemCustomisation.item_variant_id, ItemCustomisation.is_deleted==False)",
        back_populates="item_variant")
    combo_item_variants = relationship("ComboItem", back_populates="item_variant")
    variants = relationship(
        "ItemVariantVariantAssociation",
        secondary="join(ItemVariantVariantAssociation, Variant, ItemVariantVariantAssociation.variant_id == "
                  "Variant.id)",
        primaryjoin="and_(ItemVariant.id==ItemVariantVariantAssociation.item_variant_id, "
                    "ItemVariantVariantAssociation.is_deleted==False)",
        secondaryjoin="and_(Variant.id==ItemVariantVariantAssociation.variant_id, "
                      "Variant.is_deleted==False)",
        back_populates="item_variant",
        viewonly=True)


class ItemVariantVariantAssociation(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "item_variant_variant_association"

    id = Column(Integer, autoincrement=True, primary_key=True)
    item_variant_id = Column(Integer, ForeignKey('item_variant.id'), nullable=False)
    variant_id = Column(Integer, ForeignKey('variant.id'), nullable=False)
    variant = relationship(
        "Variant", primaryjoin="and_(ItemVariantVariantAssociation.variant_id==Variant.id, Variant.is_deleted==False)")
    item_variant = relationship(
        "ItemVariant",
        primaryjoin="and_(ItemVariantVariantAssociation.item_variant_id==ItemVariant.id, "
                    "ItemVariant.is_deleted==False)")


class VariantGroup(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "variant_group"

    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String, nullable=False)
    display_name = Column(String, nullable=True)
    can_select_multiple = Column(Boolean, nullable=False)
    can_select_quantity = Column(Boolean, nullable=False)
    minimum_selectable_quantity = Column(Integer, nullable=True)
    maximum_selectable_quantity = Column(Integer, nullable=True)
    is_customisation = Column(Boolean, nullable=False)
    seller_id = Column(String, ForeignKey('seller.seller_id'), nullable=False)
    variants = relationship(
        'Variant', primaryjoin="and_(VariantGroup.id==Variant.variant_group_id, Variant.is_deleted==False)",
        back_populates="variant_group")


class Variant(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "variant"

    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String, nullable=False)
    display_order = Column(Integer, nullable=False)
    variant_group_id = Column(Integer, ForeignKey('variant_group.id'), nullable=False)
    variant_group = relationship('VariantGroup', back_populates="variants")
    item_variant = relationship(
        "ItemVariant",
        secondary="join(ItemVariantVariantAssociation, ItemVariant, ItemVariantVariantAssociation.item_variant_id == "
                  "ItemVariant.id)",
        primaryjoin="and_(Variant.id==ItemVariantVariantAssociation.variant_id, "
                    "ItemVariantVariantAssociation.is_deleted==False)",
        secondaryjoin="and_(Variant.id==ItemVariantVariantAssociation.variant_id, ItemVariant.is_deleted==False)",
        uselist=False,
        viewonly=True
    )
    item_variants = relationship("ItemVariantVariantAssociation",
                                 primaryjoin="and_(Variant.id==ItemVariantVariantAssociation.variant_id, "
                                             "ItemVariantVariantAssociation.is_deleted==False)",
                                 back_populates="variant")
    item_customisations = relationship(
        "ItemCustomisation",
        primaryjoin="and_(Variant.id==ItemCustomisation.variant_id, ItemCustomisation.is_deleted==False)",
        back_populates="variant")


class ItemCustomisation(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "item_customisation"

    id = Column(Integer, autoincrement=True, primary_key=True)
    display_order = Column(Integer, nullable=False)
    delta_price = Column(DECIMAL, nullable=False)
    cost = Column(DECIMAL, nullable=False)
    item_id = Column(Integer, ForeignKey('item.id', ondelete='cascade'), nullable=True)
    variant_id = Column(Integer, ForeignKey('variant.id', ondelete='cascade'), nullable=False)
    item_variant_id = Column(Integer, ForeignKey('item_variant.id', ondelete='cascade'), nullable=True)
    item = relationship('Item', foreign_keys=[item_id], back_populates='item_customisations')
    item_variant = relationship('ItemVariant', foreign_keys=[
        item_variant_id],
        primaryjoin="and_(ItemCustomisation.item_variant_id==ItemVariant.id, "
        "ItemVariant.is_deleted==False)",
        back_populates='item_customisations')
    variant = relationship('Variant', foreign_keys=[
        variant_id], primaryjoin="and_(ItemCustomisation.variant_id==Variant.id, Variant.is_deleted==False)",
        back_populates='item_customisations')
    menu_item_item_customisations = relationship('MenuItemItemCustomisation', back_populates="item_customisation")


class SideItem(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "side_item"

    id = Column(Integer, autoincrement=True, primary_key=True)
    item_id = Column(Integer, ForeignKey('item.id'), nullable=False)
    side_item_id = Column(Integer, ForeignKey('item.id'), nullable=False)
    side_item_variant_id = Column(Integer, ForeignKey('item_variant.id'), nullable=True)
    item = relationship('Item', foreign_keys=[item_id], back_populates='side_items')
    side_item = relationship('Item', foreign_keys=[side_item_id], back_populates='side_item_of')
    side_item_variant = relationship('ItemVariant')
    menu_item_side_items = relationship('MenuItemSideItem', back_populates="side_item")


class Menu(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = 'menu'

    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column('name', String, nullable=False)
    code = Column('code', String, nullable=True)
    description = Column('description', String, nullable=True)
    display_name = Column('display_name', String, nullable=True)
    menu_types = Column('menu_types', ARRAY(String), nullable=True)
    seller_id = Column(String, ForeignKey('seller.seller_id', ondelete='cascade'), nullable=False)
    seller = relationship('Seller')

    menu_timings = relationship(
        'MenuTiming', primaryjoin="and_(Menu.id==MenuTiming.menu_id, MenuTiming.is_deleted==False)", backref='menu', )
    menu_categories = relationship('RestaurantMenuCategory',
                                   primaryjoin="and_(Menu.id==RestaurantMenuCategory.menu_id, "
                                               "RestaurantMenuCategory.is_deleted==False)",
                                   backref='menu', order_by="RestaurantMenuCategory.display_order", )

    menu_items = relationship('MenuItem',
                              primaryjoin="and_(Menu.id==MenuItem.menu_id, MenuItem.is_deleted==False,)",
                              back_populates="menu", order_by="MenuItem.display_order")
    menu_combos = relationship('MenuCombo',
                               primaryjoin="and_(Menu.id==MenuCombo.menu_id, MenuCombo.is_deleted==False,)",
                               back_populates="menu", order_by="MenuCombo.display_order")

    def __str__(self):
        return str.format('%s: %s %s' % (self.id, self.name, self.display_name))

    def __repr__(self):
        return '<Menu {}>'.format(str(self.id) + " " + self.name)


class MenuItemCategory(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "menu_item_category"

    id = Column(Integer, autoincrement=True, primary_key=True)
    menu_item_id = Column(Integer, ForeignKey('menu_item.id'), nullable=False)
    menu_category_id = Column(Integer, ForeignKey('menu_category.id'), nullable=False)
    menu_item = relationship('MenuItem', back_populates='menu_item_categories')
    menu_category = relationship(
        'RestaurantMenuCategory',
        primaryjoin="and_(RestaurantMenuCategory.id==MenuItemCategory.menu_category_id, "
                    "MenuItemCategory.is_deleted==False)",
        back_populates='menu_item_categories')

    menu = relationship(
        Menu,
        uselist=False,
        secondary=RestaurantMenuCategory.__table__,
        primaryjoin="and_(MenuItemCategory.menu_category_id==RestaurantMenuCategory.id, "
                    "MenuItemCategory.is_deleted==False)",
        secondaryjoin=RestaurantMenuCategory.menu_id == Menu.id,
        viewonly=True,
        backref="menu_item_categories",
    )


class ComboItem(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "combo_item"

    id = Column(Integer, autoincrement=True, primary_key=True)
    combo_id = Column(Integer, ForeignKey('combo.id'), nullable=False)
    item_id = Column(Integer, ForeignKey('item.id'), nullable=False)
    item_variant_id = Column(Integer, ForeignKey('item_variant.id'), nullable=True)
    item = relationship('Item', back_populates='combo_items')
    combo = relationship('Combo', back_populates='combo_items')
    item_variant = relationship('ItemVariant', back_populates='combo_item_variants')


class Combo(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "combo"

    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String, nullable=False)
    code = Column(String, nullable=True)
    description = Column(String, nullable=True)
    display_name = Column(String, nullable=True)
    image = Column(String, nullable=True)
    allergen_info = Column(String, nullable=True)
    calorie_info = Column(String, nullable=True)
    prep_time = Column(Interval, nullable=False)
    contains_alcohol = Column(Boolean, nullable=False)
    pre_tax_price = Column(DECIMAL, nullable=False)
    cost = Column(DECIMAL, nullable=False)
    sku_id = Column(Integer, nullable=False)
    sku_category_code = Column(String, nullable=False)
    active = Column(Boolean, default=True)
    sold_out = Column(Boolean, default=False)
    food_type = Column(String, nullable=False, default=FoodTypeChoices.VEGETARIAN.value)
    seller_id = Column(String, ForeignKey('seller.seller_id'), nullable=True)
    seller = relationship('Seller')
    combo_items = relationship(
        'ComboItem', primaryjoin="and_(Combo.id==ComboItem.combo_id, ComboItem.is_deleted==False)",
        back_populates="combo")
    menu_combos = relationship('MenuCombo', back_populates="combo")


class MenuCombo(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "menu_combo"

    id = Column(Integer, autoincrement=True, primary_key=True)
    menu_id = Column(Integer, ForeignKey('menu.id'), nullable=False)
    combo_id = Column(Integer, ForeignKey('combo.id'), nullable=False)
    display_order = Column(Integer, nullable=False)
    sold_out = Column(Boolean, nullable=False)
    menu = relationship('Menu', back_populates='menu_combos')
    combo = relationship('Combo', back_populates='menu_combos')
    menu_combo_categories = relationship(
        'MenuComboCategory',
        primaryjoin="and_(MenuCombo.id==MenuComboCategory.menu_combo_id, MenuComboCategory.is_deleted==False)",
        back_populates="menu_combo")


class MenuComboCategory(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "menu_combo_category"

    id = Column(Integer, autoincrement=True, primary_key=True)
    menu_combo_id = Column(Integer, ForeignKey('menu_combo.id'), nullable=False)
    menu_category_id = Column(Integer, ForeignKey('menu_category.id'), nullable=False)
    menu_combo = relationship('MenuCombo', back_populates='menu_combo_categories')
    menu_category = relationship('RestaurantMenuCategory', back_populates='menu_combo_categories')

    menu = relationship(
        Menu,
        uselist=False,
        secondary=RestaurantMenuCategory.__table__,
        primaryjoin="and_(MenuComboCategory.menu_category_id==RestaurantMenuCategory.id, "
                    "MenuComboCategory.is_deleted==False)",
        secondaryjoin=RestaurantMenuCategory.menu_id == Menu.id,
        viewonly=True,
        backref="menu_combo_categories",
    )


class MenuItemItemVariant(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "menu_item_item_variant"

    id = Column(Integer, autoincrement=True, primary_key=True)
    menu_item_id = Column(Integer, ForeignKey('menu_item.id'), nullable=False)
    item_variant_id = Column(Integer, ForeignKey('item_variant.id'), nullable=False)
    menu_item = relationship('MenuItem', back_populates='menu_item_item_variants')
    item_variant = relationship('ItemVariant', back_populates='menu_item_item_variants')

    menu = relationship(
        Menu,
        uselist=False,
        secondary=MenuItem.__table__,
        primaryjoin="and_(MenuItemItemVariant.menu_item_id==MenuItem.id, MenuItemItemVariant.is_deleted==False)",
        secondaryjoin=MenuItem.menu_id == Menu.id,
        viewonly=True,
        backref="menu_item_item_variants"
    )


class MenuItemSideItem(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "menu_item_side_item"
    id = Column(Integer, autoincrement=True, primary_key=True)
    menu_item_id = Column(Integer, ForeignKey('menu_item.id'), nullable=False)
    side_item_id = Column(Integer, ForeignKey('side_item.id'), nullable=False)

    menu_item = relationship('MenuItem', back_populates='menu_item_side_items')
    side_item = relationship('SideItem', back_populates='menu_item_side_items')

    menu = relationship(
        Menu,
        uselist=False,
        secondary=MenuItem.__table__,
        primaryjoin="and_(MenuItemSideItem.menu_item_id==MenuItem.id, MenuItemSideItem.is_deleted==False)",
        secondaryjoin=MenuItem.menu_id == Menu.id,
        viewonly=True,
        backref="menu_item_side_items",
    )


class MenuItemItemCustomisation(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "menu_item_item_customisation"
    id = Column(Integer, autoincrement=True, primary_key=True)
    menu_item_id = Column(Integer, ForeignKey('menu_item.id'), nullable=False)
    item_customisation_id = Column(Integer, ForeignKey('item_customisation.id'), nullable=False)

    menu_item = relationship('MenuItem', back_populates='menu_item_item_customisations')
    item_customisation = relationship('ItemCustomisation', back_populates='menu_item_item_customisations')

    menu = relationship(
        Menu,
        uselist=False,
        secondary=MenuItem.__table__,
        primaryjoin="and_(MenuItemItemCustomisation.menu_item_id==MenuItem.id, "
                    "MenuItemItemCustomisation.is_deleted==False)",
        secondaryjoin=MenuItem.menu_id == Menu.id,
        viewonly=True,
        backref="menu_item_item_customisations",
    )


class RoomRackRateModel(Base, TimeStampMixin):
    __tablename__ = 'room_rack_rate'

    room_rack_rate_id = Column(String, primary_key=True)
    property_id = Column(ForeignKey('property.id'), nullable=False)
    room_type_id = Column(ForeignKey('room_type.id'), nullable=False)
    adult_count = Column(Integer, nullable=False)
    rack_rate = Column(NUMERIC(precision=15, scale=4), nullable=False)

    property = relationship('Property')
    room_type = relationship('RoomType')

    __table_args__ = (
        UniqueConstraint("property_id", "room_type_id", "adult_count"),
    )

    def fill_room_rack_rate_id(self):
        self.room_rack_rate_id = str.format('%s-%s-%s' % (self.property_id, self.room_type.type, self.adult_count))

    def __str__(self):
        return str.format('%s: %s, %s' % (self.property.name, self.room_type.type, self.adult_count))


class Kitchen(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "kitchen"

    id = Column(Integer, autoincrement=True, primary_key=True)
    property_id = Column(String, ForeignKey('property.id'), nullable=False)
    name = Column(String, nullable=False)
    config = Column(JSON)
    property = relationship('Property')

    def __str__(self):
        return str.format('%s: %s' % (self.name, self.property.name))


class SellerSkuCategory(Base, TimeStampMixin):
    __tablename__ = 'seller_sku_category'

    seller_id = Column(String, ForeignKey('seller.seller_id'), primary_key=True)
    sku_category_id = Column(Integer, ForeignKey('sku_category.id'), primary_key=True)

    seller = relationship('Seller')
    sku_category = relationship('SkuCategory')


# Department Management Models

class Department(Base, TimeStampMixin):
    """Tenant-level department model for hierarchical organization"""
    __tablename__ = 'department'

    id = Column(Integer, primary_key=True)
    parent_id = Column(Integer, ForeignKey('department.id'), nullable=True)
    name = Column(String(255), nullable=False)
    code = Column(String(50), nullable=False, unique=True)
    description = Column(Text)
    financial_code = Column(String(20))
    is_active = Column(Boolean, default=True)
    auto_create_on_property_launch = Column(Boolean, default=False)
    config = Column(JSON)  # USALI compliance, reporting configs, GL mappings

    # Relationships
    parent = relationship("Department", remote_side=[id], backref="children")
    brand_mappings = relationship("BrandDepartmentMapping", back_populates="department")
    property_departments = relationship("PropertyDepartment", back_populates="tenant_department")
    sku_mappings = relationship("SkuDepartmentMapping", back_populates="department")
    seller_templates = relationship("SellerTemplate", back_populates="default_department")

    def __repr__(self):
        return '<Department {} ({})>'.format(self.name, self.code)

    @python_property
    def full_hierarchy_name(self):
        """Get full hierarchical name like 'F&B > Restaurant > Fine Dining'"""
        if self.parent:
            return "{} > {}".format(self.parent.full_hierarchy_name, self.name)
        return self.name


class BrandDepartmentMapping(Base, TimeStampMixin):
    """Mapping between brands and departments"""
    __tablename__ = 'brand_department_mapping'

    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brand.id'), nullable=False)
    department_id = Column(Integer, ForeignKey('department.id'), nullable=False)
    is_active = Column(Boolean, default=True)

    # Relationships
    brand = relationship("Brand", backref="department_mappings")
    department = relationship("Department", back_populates="brand_mappings")

    __table_args__ = (
        UniqueConstraint('brand_id', 'department_id'),
        Index('idx_brand_department_brand_id', 'brand_id'),
        Index('idx_brand_department_dept_id', 'department_id'),
    )


class PropertyDepartment(Base, TimeStampMixin):
    """Property-specific department instances"""
    __tablename__ = 'property_department'

    id = Column(Integer, primary_key=True)
    property_id = Column(String, ForeignKey('property.id'), nullable=False)
    tenant_department_id = Column(Integer, ForeignKey('department.id'), nullable=True)
    inherited_from_brand_id = Column(Integer, ForeignKey('brand.id'), nullable=True)
    parent_id = Column(Integer, ForeignKey('property_department.id'), nullable=True)
    name = Column(String(255), nullable=False)
    code = Column(String(50), nullable=False)
    description = Column(Text)
    financial_code = Column(String(20))
    is_active = Column(Boolean, default=True)
    is_custom = Column(Boolean, default=False)  # True if custom to property, False if inherited
    config = Column(JSON)  # Property-specific configurations

    # Relationships
    property = relationship("Property", backref="departments")
    tenant_department = relationship("Department", back_populates="property_departments")
    parent = relationship("PropertyDepartment", remote_side=[id], backref="children")
    sellers = relationship("Seller", back_populates="default_department")
    sku_mappings = relationship("PropertySkuDepartment", back_populates="property_department")

    __table_args__ = (
        UniqueConstraint('property_id', 'code'),
        Index('idx_property_department_property_id', 'property_id'),
        Index('idx_property_department_tenant_dept', 'tenant_department_id'),
    )


class SkuDepartmentMapping(Base, TimeStampMixin):
    """Mapping between SKUs and tenant departments"""
    __tablename__ = 'sku_department_mapping'

    id = Column(Integer, primary_key=True)
    sku_id = Column(Integer, ForeignKey('sku.id'), nullable=False)
    department_id = Column(Integer, ForeignKey('department.id'), nullable=False)
    effective_from = Column(DateTime, nullable=True)
    effective_to = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

    # Relationships
    sku = relationship("Sku", backref="department_mappings")
    department = relationship("Department", back_populates="sku_mappings")

    __table_args__ = (
        UniqueConstraint('sku_id', 'department_id'),
        Index('idx_sku_department_sku_id', 'sku_id'),
        Index('idx_sku_department_dept_id', 'department_id'),
    )


class PropertySkuDepartment(Base, TimeStampMixin):
    """Property-specific SKU-department mappings"""
    __tablename__ = 'property_sku_department'

    id = Column(Integer, primary_key=True)
    property_id = Column(String, ForeignKey('property.id'), nullable=False)
    sku_id = Column(Integer, ForeignKey('sku.id'), nullable=False)
    property_department_id = Column(Integer, ForeignKey('property_department.id'), nullable=False)
    effective_from = Column(DateTime, nullable=True)
    effective_to = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

    # Relationships
    property = relationship("Property", backref="sku_department_mappings")
    sku = relationship("Sku", backref="property_department_mappings")
    property_department = relationship("PropertyDepartment", back_populates="sku_mappings")

    __table_args__ = (
        UniqueConstraint('property_id', 'sku_id', 'property_department_id'),
        Index('idx_prop_sku_dept_property_id', 'property_id'),
        Index('idx_prop_sku_dept_sku_id', 'sku_id'),
        Index('idx_prop_sku_dept_dept_id', 'property_department_id'),
    )


class SellerTemplate(Base, TimeStampMixin):
    """Templates for seller creation with default department assignments (Terminal Type Template)"""
    __tablename__ = 'seller_template'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    code = Column(String(50), nullable=False, unique=True)
    description = Column(Text)
    default_department_id = Column(Integer, ForeignKey('department.id'), nullable=True)
    is_active = Column(Boolean, default=True)
    auto_create_on_property_launch = Column(Boolean, default=False)
    template_config = Column(JSON)  # Store template configuration (GL codes, ERP mappings, etc.)

    # Relationships
    default_department = relationship("Department", back_populates="seller_templates")
    sku_assignments = relationship("SkuTerminalTypeAssignment", back_populates="terminal_type_template")
    sellers = relationship("Seller", back_populates="terminal_type")

    def __repr__(self):
        return '<SellerTemplate {} ({})>'.format(self.name, self.code)


class SkuTerminalTypeAssignment(Base, TimeStampMixin):
    """Assignment of SKUs to terminal types (seller templates)"""
    __tablename__ = 'sku_terminal_type_assignment'

    id = Column(Integer, primary_key=True)
    sku_id = Column(Integer, ForeignKey('sku.id'), nullable=False)
    terminal_type_template_id = Column(Integer, ForeignKey('seller_template.id'), nullable=False)
    is_active = Column(Boolean, default=True)

    # Relationships
    sku = relationship("Sku", backref="terminal_type_assignments")
    terminal_type_template = relationship("SellerTemplate", back_populates="sku_assignments")

    __table_args__ = (
        UniqueConstraint('sku_id', 'terminal_type_template_id'),
        Index('idx_sku_terminal_type_sku_id', 'sku_id'),
        Index('idx_sku_terminal_type_template_id', 'terminal_type_template_id'),
    )


class TransactionMaster(Base, TimeStampMixin):
    """Master table for transaction codes with automated generation and dual entity support"""
    __tablename__ = 'transaction_master'

    id = Column(Integer, primary_key=True)
    transaction_code = Column(String(50), nullable=False, unique=True)
    name = Column(String(255), nullable=False)
    entity_type = Column(String(50), nullable=False)  # SKU, PaymentMethod, Department, etc.
    transaction_type = Column(String(50), nullable=False)  # SALE, REFUND, PAYMENT, etc.
    operational_unit_id = Column(String, nullable=True)  # Property ID, Department ID, etc.
    operational_unit_type = Column(String(50), nullable=True)  # PROPERTY, DEPARTMENT, etc.
    source = Column(String(100), nullable=True)  # AUTO_GENERATED, MANUAL, IMPORT
    gl_code = Column(String(20), nullable=True)  # General Ledger Code
    erp_id = Column(String(50), nullable=True)  # ERP System ID
    is_merge = Column(Boolean, default=False)  # For financial aggregation
    particulars = Column(Text, nullable=True)  # Transaction description
    status = Column(String(20), default='ACTIVE')  # ACTIVE, INACTIVE, PENDING
    hotel_entity_id = Column(String, nullable=True)  # Hotel entity for dual accounting
    franchiser_entity_id = Column(String, nullable=True)  # Franchiser entity for dual accounting

    # USALI compliance fields
    usali_code = Column(String(20), nullable=True)
    usali_category = Column(String(100), nullable=True)

    __table_args__ = (
        Index('idx_transaction_master_code', 'transaction_code'),
        Index('idx_transaction_master_entity_type', 'entity_type'),
        Index('idx_transaction_master_operational_unit', 'operational_unit_id', 'operational_unit_type'),
        Index('idx_transaction_master_status', 'status'),
    )

    def __repr__(self):
        return '<TransactionMaster {}: {}>'.format(self.transaction_code, self.name)


class PaymentMethod(Base, TimeStampMixin):
    """Tenant-level payment method definitions"""
    __tablename__ = 'payment_method'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)  # "Credit Card", "Cash", "UPI"
    code = Column(String(50), nullable=False, unique=True)
    payment_method_type = Column(String(100), nullable=True)  # "card", "cash", "digital"
    paid_to = Column(String(100), nullable=True)  # "hotel", "third_party", "corporate"
    allowed_paid_by = Column(String(100), nullable=True)  # "guest", "corporate", "staff"
    auto_create_on_property_launch = Column(Boolean, default=False)
    config = Column(JSON, nullable=True)  # Processing fees, limits, integration settings
    is_active = Column(Boolean, default=True)

    # Relationships
    property_payment_methods = relationship("PropertyPaymentMethod", back_populates="payment_method")

    def __repr__(self):
        return '<PaymentMethod {} ({})>'.format(self.name, self.code)


class PropertyPaymentMethod(Base, TimeStampMixin):
    """Property-level payment method instances"""
    __tablename__ = 'property_payment_method'

    id = Column(Integer, primary_key=True)
    property_id = Column(String, ForeignKey('property.id'), nullable=False)
    payment_method_id = Column(Integer, ForeignKey('payment_method.id'), nullable=True)
    name = Column(String(255), nullable=False)
    code = Column(String(50), nullable=False)
    is_custom = Column(Boolean, default=False)  # Property-added methods
    config = Column(JSON, nullable=True)  # Property-specific overrides
    is_active = Column(Boolean, default=True)

    # Relationships
    property = relationship("Property", backref="payment_methods")
    payment_method = relationship("PaymentMethod", back_populates="property_payment_methods")

    __table_args__ = (
        UniqueConstraint('property_id', 'code'),
        Index('idx_property_payment_method_property_id', 'property_id'),
        Index('idx_property_payment_method_payment_method_id', 'payment_method_id'),
    )


class TransactionDefaultMapping(Base, TimeStampMixin):
    """Default mapping configuration for transaction code generation"""
    __tablename__ = 'transaction_default_mapping'

    id = Column(Integer, primary_key=True)
    triggering_entity_type = Column(String(50), nullable=False)  # 'SKU', 'PaymentMethod'
    triggering_entity_category = Column(String(100), nullable=False)  # SKU category, payment type
    entity_type = Column(String(20), nullable=False)  # 'Hotel', 'Franchiser'
    default_gl_code = Column(String(50), nullable=True)
    default_erp_id = Column(String(50), nullable=True)
    default_particulars = Column(Text, nullable=True)
    default_is_merge = Column(Boolean, default=True)
    is_active = Column(Boolean, default=True)

    __table_args__ = (
        UniqueConstraint('triggering_entity_type', 'triggering_entity_category', 'entity_type'),
        Index('idx_transaction_default_mapping_entity_type', 'triggering_entity_type'),
    )
