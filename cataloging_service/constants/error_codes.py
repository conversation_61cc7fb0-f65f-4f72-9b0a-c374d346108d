ERROR_STATUS = "error"


def get(key):
    return __code.get(key, None)


__code = dict()

GENERIC_EXCEPTION = "CS000"
__code[GENERIC_EXCEPTION] = {
    "code": GENERIC_EXCEPTION,
    "msg": "Internal Server error",
    "status_code": 500,
}

FAILED_TO_UPLOAD_FILE = "CS101"
__code[FAILED_TO_UPLOAD_FILE] = {
    "code": FAILED_TO_UPLOAD_FILE,
    "msg": "Invalid Username or Password",
    "status_code": 500,
}

COUNTRY_NOT_FOUND = "CS102"
__code[COUNTRY_NOT_FOUND] = {
    "code": COUNTRY_NOT_FOUND,
    "msg": "Country with the given id is not present",
    "status_code": 404,
}

STATE_NOT_FOUND = "CS103"
__code[STATE_NOT_FOUND] = {
    "code": STATE_NOT_FOUND,
    "msg": "State with the given id is not present",
    "status_code": 404,
}

CITY_NOT_FOUND = "CS104"
__code[CITY_NOT_FOUND] = {
    "code": CITY_NOT_FOUND,
    "msg": "City with the given id is not present",
    "status_code": 404,
}

INVALID_REQUEST_DATA = "CS105"
__code[INVALID_REQUEST_DATA] = {
    "code": INVALID_REQUEST_DATA,
    "msg": "Invalid request data",
    "status_code": 400,
}

EMAIL_REQUIRED_FOR_PRIMARY_OWNER = "CS106"
__code[EMAIL_REQUIRED_FOR_PRIMARY_OWNER] = {
    "code": EMAIL_REQUIRED_FOR_PRIMARY_OWNER,
    "msg": "Primary Owner needs email",
    "status_code": 400,
}

PHONE_REQUIRED_FOR_PRIMARY_OWNER = "CS107"
__code[PHONE_REQUIRED_FOR_PRIMARY_OWNER] = {
    "code": PHONE_REQUIRED_FOR_PRIMARY_OWNER,
    "msg": "Primary Owner needs phone number",
    "status_code": 400,
}

PROPERTY_NOT_FOUND = "CS108"
__code[PROPERTY_NOT_FOUND] = {
    "code": PROPERTY_NOT_FOUND,
    "msg": "Property with given id could not be found",
    "status_code": 404,
}

DUPLICATE_ROOM_TYPE_CONFIGURATION = "CS109"
__code[DUPLICATE_ROOM_TYPE_CONFIGURATION] = {
    "code": DUPLICATE_ROOM_TYPE_CONFIGURATION,
    "msg": "Room Type Config is duplicate",
    "status_code": 400,
}

ROOM_TYPE_NOT_FOUND = "CS110"
__code[ROOM_TYPE_NOT_FOUND] = {
    "code": ROOM_TYPE_NOT_FOUND,
    "msg": "Room Type could not be found",
    "status_code": 404,
}

ROOM_TYPE_CONFIG_NOT_FOUND = "CS111"
__code[ROOM_TYPE_CONFIG_NOT_FOUND] = {
    "code": ROOM_TYPE_CONFIG_NOT_FOUND,
    "msg": "Room Type Config could not be found",
    "status_code": 404,
}

DUPLICATE_ROOM = "CS112"
__code[DUPLICATE_ROOM] = {"code": DUPLICATE_ROOM, "msg": "Room Exists", "status_code": 400}

ROOM_NOT_FOUND = "CS113"
__code[ROOM_NOT_FOUND] = {"code": ROOM_NOT_FOUND, "msg": "Room Not found", "status_code": 404}

INVALID_MESSAGE = "CS114"
__code[INVALID_MESSAGE] = {"code": INVALID_MESSAGE, "msg": "Invalid Message", "status_code": 500}

INVALID_EMAIL = "CS115"
__code[INVALID_EMAIL] = {"code": INVALID_EMAIL, "msg": "Invalid Email", "status_code": 400}

INVALID_IFSC_CODE = "CS116"
__code[INVALID_IFSC_CODE] = {
    "code": INVALID_IFSC_CODE,
    "msg": "Invalid IFSC Code",
    "status_code": 400,
}

SIGNED_DATE_REQUIRED = "CS117"
__code[SIGNED_DATE_REQUIRED] = {
    "code": SIGNED_DATE_REQUIRED,
    "msg": "Signed date required",
    "status_code": 400,
}

CONTRACTUAL_LAUNCH_DATE_REQUIRED = "CS118"
__code[CONTRACTUAL_LAUNCH_DATE_REQUIRED] = {
    "code": CONTRACTUAL_LAUNCH_DATE_REQUIRED,
    "msg": "Contractual launch date required",
    "status_code": 400,
}

LAUNCH_DATE_REQUIRED = "CS119"
__code[LAUNCH_DATE_REQUIRED] = {
    "code": LAUNCH_DATE_REQUIRED,
    "msg": "Launch date required",
    "status_code": 400,
}

CHURNED_DATE_REQUIRED = "CS120"
__code[CHURNED_DATE_REQUIRED] = {
    "code": CHURNED_DATE_REQUIRED,
    "msg": "Churned date required",
    "status_code": 400,
}

INVALID_DATE_ORDER = "CS121"
__code[INVALID_DATE_ORDER] = {
    "code": INVALID_DATE_ORDER,
    "msg": "Dates should be in the order Signed Date <= Launch Date <= Churned Date",
    "status_code": 400,
}

PROPERTY_CANNOT_BE_SIGNED = "CS122"
__code[PROPERTY_CANNOT_BE_SIGNED] = {
    "code": PROPERTY_CANNOT_BE_SIGNED,
    "msg": "Property Cannot be signed",
    "status_code": 400,
}

PROPERTY_CANNOT_BE_LAUNCHED = "CS123"
__code[PROPERTY_CANNOT_BE_LAUNCHED] = {
    "code": PROPERTY_CANNOT_BE_LAUNCHED,
    "msg": "Property Cannot be launched",
    "status_code": 400,
}

INVALID_OCCUPANCY = "CS124"
__code[INVALID_OCCUPANCY] = {
    "code": INVALID_OCCUPANCY,
    "msg": "Min occupancy should be <= min(adults, children) and max_total <= (adults + children). Negative Values not accepted",
    "status_code": 400,
}

STATUS_NOT_PERMITTED = "CS125"
__code[STATUS_NOT_PERMITTED] = {
    "code": STATUS_NOT_PERMITTED,
    "msg": "Status cannot be live or signed from migration",
    "status_code": 400,
}

PROPERTY_ID_CLASHED = "CS126"
__code[PROPERTY_ID_CLASHED] = {
    "code": PROPERTY_ID_CLASHED,
    "msg": "Property id clashing with existing ID",
    "status_code": 500,
}

REGION_NOT_FOUND = "CS127"
__code[REGION_NOT_FOUND] = {
    "code": REGION_NOT_FOUND,
    "msg": "Region with the given id is not present",
    "status_code": 404,
}

CLUSTER_NOT_FOUND = "CS128"
__code[CLUSTER_NOT_FOUND] = {
    "code": CLUSTER_NOT_FOUND,
    "msg": "Cluster with the given id is not present",
    "status_code": 404,
}

RMQ_CONNECTION_ERROR = "CS129"
__code[RMQ_CONNECTION_ERROR] = {
    "code": RMQ_CONNECTION_ERROR,
    "msg": "Could not connect to RabbitMQ broker",
    "status_code": 500,
}

ROOM_TYPE_CONFIG_INVALID = "CS129"
__code[ROOM_TYPE_CONFIG_INVALID] = {
    "code": ROOM_TYPE_CONFIG_INVALID,
    "msg": "Invalid data in room type config. Min occupancy should not be 0 and should be <= max(adults, children) "
    "and max_total <= (adults + children) and max_total should not be empty or 0. Negative Values not accepted",
    "status_code": 400,
}

ROOM_TYPE_CONFIG_CANNOT_CHANGE = "CS130"
__code[ROOM_TYPE_CONFIG_CANNOT_CHANGE] = {
    "code": ROOM_TYPE_CONFIG_CANNOT_CHANGE,
    "msg": "Cannot change the room type of Room Type Config",
    "status_code": 404,
}

INVALID_OTA_PROPERTY_CONFIG = "CS131"
__code[INVALID_OTA_PROPERTY_CONFIG] = {
    "code": INVALID_OTA_PROPERTY_CONFIG,
    "msg": "Ota Property configuration is invalid",
    "status_code": 400,
}

RCS_ERROR = "CS132"
__code[RCS_ERROR] = {"code": RCS_ERROR, "msg": "RCS Error", "status_code": 500}

UNIRATE_ERROR = "CS133"
__code[UNIRATE_ERROR] = {"code": UNIRATE_ERROR, "msg": "Unirate Error", "status_code": 500}

OTA_PROPERTY_NOT_FOUND = "CS134"
__code[OTA_PROPERTY_NOT_FOUND] = {
    "code": OTA_PROPERTY_NOT_FOUND,
    "msg": "OTA-Property not Found",
    "status_code": 404,
}

PROMO_UTILITY_ERROR = "CS135"
__code[PROMO_UTILITY_ERROR] = {
    "code": PROMO_UTILITY_ERROR,
    "msg": "Promo utility Error",
    "status_code": 500,
}

ITS_ERROR = "CS136"
__code[ITS_ERROR] = {"code": ITS_ERROR, "msg": "ITS Error", "status_code": 500}

PRICING_ERROR = "CS137"
__code[PRICING_ERROR] = {"code": PRICING_ERROR, "msg": "Pricing Error", "status_code": 500}

OTA_NOT_FOUND = "CS138"
__code[OTA_NOT_FOUND] = {"code": OTA_NOT_FOUND, "msg": "OTA Not Found", "status_code": 404}

PROPERTY_NOT_LIVE = "CS139"
__code[PROPERTY_NOT_LIVE] = {
    "code": PROPERTY_NOT_LIVE,
    "msg": "Property Not Live",
    "status_code": 400,
}

SKU_CATEGORY_NOT_FOUND = "CS140"
__code[SKU_CATEGORY_NOT_FOUND] = {
    "code": SKU_CATEGORY_NOT_FOUND,
    "msg": "Sku Category with given id could not be found",
    "status_code": 404,
}

INTERNAL_SERVER_ERROR = "CS141"
__code[INTERNAL_SERVER_ERROR] = {
    "code": INTERNAL_SERVER_ERROR,
    "msg": "Unexpected error occured",
    "status_code": 500,
}

CHANNEL_NOT_FOUND = "CS142"
__code[CHANNEL_NOT_FOUND] = {
    "code": CHANNEL_NOT_FOUND,
    "msg": "Channel with given id could not be found",
    "status_code": 404,
}

PROVIDER_NOT_FOUND = "CS143"
__code[PROVIDER_NOT_FOUND] = {
    "code": PROVIDER_NOT_FOUND,
    "msg": "Provider with given code could not be found",
    "status_code": 404,
}

INVALID_PROVIDER = "CS144"
__code[INVALID_PROVIDER] = {
    "code": INVALID_PROVIDER,
    "msg": "Provider with given code is invalid",
    "status_code": 400,
}

METADATA_NOT_FOUND = "CS145"
__code[METADATA_NOT_FOUND] = {
    "code": METADATA_NOT_FOUND,
    "msg": "metadata with given name could not be found",
    "status_code": 404,
}

INVALID_RULE_DEFINITION = "CS146"
__code[INVALID_RULE_DEFINITION] = {
    "code": INVALID_RULE_DEFINITION,
    "msg": "SKU has to be present in current list of sku",
    "status_code": 400,
}

SKU_NOT_FOUND = "CS147"
__code[SKU_NOT_FOUND] = {
    "code": SKU_NOT_FOUND,
    "msg": "Sku with given id could not be found",
    "status_code": 404,
}

PARAM_NOT_FOUND = "CS148"
__code[PARAM_NOT_FOUND] = {
    "code": PARAM_NOT_FOUND,
    "msg": "Param with given id could not be found",
    "status_code": 404,
}

PROPERTY_SKU_NOT_FOUND = "CS149"
__code[PROPERTY_SKU_NOT_FOUND] = {
    "code": PROPERTY_SKU_NOT_FOUND,
    "msg": "Property Sku with given property and skus could not be found",
    "status_code": 404,
}

INVALID_CHURNED_DATE = "CS150"
__code[INVALID_CHURNED_DATE] = {
    "code": INVALID_CHURNED_DATE,
    "msg": "Churned date should be less than or equal to current date.",
    "status_code": 400,
}

INVALID_TRANSACTION_DATE_FORMAT = "CS151"
__code[INVALID_TRANSACTION_DATE_FORMAT] = {
    "code": INVALID_TRANSACTION_DATE_FORMAT,
    "msg": "Incorrect trasaction date format, should be YYYY-MM-DD",
    "status_code": 400,
}

PROPERTY_ID_NOT_GIVEN = "CS152"
__code[PROPERTY_ID_NOT_GIVEN] = {
    "code": PROPERTY_ID_NOT_GIVEN,
    "msg": "Property id is not given",
    "status_code": 400,
}

SELLER_NOT_FOUND = "CS153"
__code[SELLER_NOT_FOUND] = {
    "code": SELLER_NOT_FOUND,
    "msg": "Seller with given id could not be found",
    "status_code": 404,
}

ROOM_RACK_RATE_NOT_FOUND = "CS154"
__code[ROOM_RACK_RATE_NOT_FOUND] = {
    "code": ROOM_RACK_RATE_NOT_FOUND,
    "msg": "Rack Rate for given room type and occupancy is not configured",
    "status_code": 404,
}

STATE_NAME_NOT_FOUND = "CS155"
__code[STATE_NAME_NOT_FOUND] = {
    "code": STATE_NAME_NOT_FOUND,
    "msg": "State with the given name is not present",
    "status_code": 404,
}

INVALID_SELLER_REQUEST_HEADERS = "CS156"
__code[INVALID_SELLER_REQUEST_HEADERS] = {
    "code": INVALID_SELLER_REQUEST_HEADERS,
    "msg": "You're not authorized to access the resource",
    "status_code": 401,
}

ROLLOVER_BEYOND_CURRENT_DATE = "CS157"
__code[ROLLOVER_BEYOND_CURRENT_DATE] = {
    'code': ROLLOVER_BEYOND_CURRENT_DATE,
    'msg': "Business date cannot be greater than current calendar date",
    'status_code': 400
}

DUPLICATE_MENU_CODE = "CS158"
__code[DUPLICATE_MENU_CODE] = {
    "code": DUPLICATE_MENU_CODE,
    "msg": "Menu code is duplicate",
    "status_code": 400,
}

DUPLICATE_COMBO_CODE = "CS159"
__code[DUPLICATE_COMBO_CODE] = {
    "code": DUPLICATE_COMBO_CODE,
    "msg": "Combo code is duplicate",
    "status_code": 400,
}

DUPLICATE_ITEM_CODE = "CS160"
__code[DUPLICATE_ITEM_CODE] = {
    "code": DUPLICATE_ITEM_CODE,
    "msg": "Item code is duplicate",
    "status_code": 400,
}

MENU_ITEM_NOT_FOUND = "CS160"
__code[MENU_ITEM_NOT_FOUND] = {
    "code": MENU_ITEM_NOT_FOUND,
    "msg": "Menu Item not found",
    "status_code": 404,
}

ROOM_TYPE_CONFIG_FIELD_NULL = "CS161"
__code[ROOM_TYPE_CONFIG_FIELD_NULL] = {
    "code": ROOM_TYPE_CONFIG_FIELD_NULL,
    "msg": "Invalid data in room type config, adults and children field should not be empty or null.",
    "status_code": 400,
}


PROPERTY_AND_SELLER_PROPERTY_MISMATCH = "CS162"
__code[PROPERTY_AND_SELLER_PROPERTY_MISMATCH] = {
    "code": PROPERTY_AND_SELLER_PROPERTY_MISMATCH,
    "msg": "Seller is not configured for this property.",
    "status_code": 400,
}

# Department Management Error Codes
DEPARTMENT_NOT_FOUND = "CS163"
__code[DEPARTMENT_NOT_FOUND] = {
    "code": DEPARTMENT_NOT_FOUND,
    "msg": "Department with given id could not be found",
    "status_code": 404,
}

DEPARTMENT_CODE_ALREADY_EXISTS = "CS164"
__code[DEPARTMENT_CODE_ALREADY_EXISTS] = {
    "code": DEPARTMENT_CODE_ALREADY_EXISTS,
    "msg": "Department with this code already exists",
    "status_code": 400,
}

DEPARTMENT_HAS_CHILDREN = "CS165"
__code[DEPARTMENT_HAS_CHILDREN] = {
    "code": DEPARTMENT_HAS_CHILDREN,
    "msg": "Cannot delete department that has child departments",
    "status_code": 400,
}

PARENT_DEPARTMENT_NOT_FOUND = "CS166"
__code[PARENT_DEPARTMENT_NOT_FOUND] = {
    "code": PARENT_DEPARTMENT_NOT_FOUND,
    "msg": "Parent department not found",
    "status_code": 404,
}

PARENT_DEPARTMENT_INACTIVE = "CS167"
__code[PARENT_DEPARTMENT_INACTIVE] = {
    "code": PARENT_DEPARTMENT_INACTIVE,
    "msg": "Parent department is inactive",
    "status_code": 400,
}

DEPARTMENT_CANNOT_BE_PARENT_OF_ITSELF = "CS168"
__code[DEPARTMENT_CANNOT_BE_PARENT_OF_ITSELF] = {
    "code": DEPARTMENT_CANNOT_BE_PARENT_OF_ITSELF,
    "msg": "Department cannot be parent of itself",
    "status_code": 400,
}

DEPARTMENT_INACTIVE = "CS169"
__code[DEPARTMENT_INACTIVE] = {
    "code": DEPARTMENT_INACTIVE,
    "msg": "Department is inactive",
    "status_code": 400,
}

BRAND_DEPARTMENT_MAPPING_EXISTS = "CS170"
__code[BRAND_DEPARTMENT_MAPPING_EXISTS] = {
    "code": BRAND_DEPARTMENT_MAPPING_EXISTS,
    "msg": "Brand-department mapping already exists",
    "status_code": 400,
}

PROPERTY_DEPARTMENT_NOT_FOUND = "CS171"
__code[PROPERTY_DEPARTMENT_NOT_FOUND] = {
    "code": PROPERTY_DEPARTMENT_NOT_FOUND,
    "msg": "Property department not found",
    "status_code": 404,
}

PROPERTY_DEPARTMENT_CODE_EXISTS = "CS172"
__code[PROPERTY_DEPARTMENT_CODE_EXISTS] = {
    "code": PROPERTY_DEPARTMENT_CODE_EXISTS,
    "msg": "Property department with this code already exists",
    "status_code": 400,
}

TENANT_DEPARTMENT_INACTIVE = "CS173"
__code[TENANT_DEPARTMENT_INACTIVE] = {
    "code": TENANT_DEPARTMENT_INACTIVE,
    "msg": "Tenant department is inactive",
    "status_code": 400,
}

PARENT_PROPERTY_DEPARTMENT_NOT_FOUND = "CS174"
__code[PARENT_PROPERTY_DEPARTMENT_NOT_FOUND] = {
    "code": PARENT_PROPERTY_DEPARTMENT_NOT_FOUND,
    "msg": "Parent property department not found",
    "status_code": 404,
}

PROPERTY_DEPARTMENT_CANNOT_BE_PARENT_OF_ITSELF = "CS175"
__code[PROPERTY_DEPARTMENT_CANNOT_BE_PARENT_OF_ITSELF] = {
    "code": PROPERTY_DEPARTMENT_CANNOT_BE_PARENT_OF_ITSELF,
    "msg": "Property department cannot be parent of itself",
    "status_code": 400,
}

PROPERTY_DEPARTMENT_PROPERTY_MISMATCH = "CS176"
__code[PROPERTY_DEPARTMENT_PROPERTY_MISMATCH] = {
    "code": PROPERTY_DEPARTMENT_PROPERTY_MISMATCH,
    "msg": "Property department does not belong to the specified property",
    "status_code": 400,
}

SKU_DEPARTMENT_MAPPING_EXISTS = "CS177"
__code[SKU_DEPARTMENT_MAPPING_EXISTS] = {
    "code": SKU_DEPARTMENT_MAPPING_EXISTS,
    "msg": "SKU-department mapping already exists",
    "status_code": 400,
}

PROPERTY_SKU_DEPARTMENT_MAPPING_EXISTS = "CS178"
__code[PROPERTY_SKU_DEPARTMENT_MAPPING_EXISTS] = {
    "code": PROPERTY_SKU_DEPARTMENT_MAPPING_EXISTS,
    "msg": "Property-SKU-department mapping already exists",
    "status_code": 400,
}

SELLER_TEMPLATE_NOT_FOUND = "CS179"
__code[SELLER_TEMPLATE_NOT_FOUND] = {
    "code": SELLER_TEMPLATE_NOT_FOUND,
    "msg": "Seller template not found",
    "status_code": 404,
}

SKU_TERMINAL_TYPE_ASSIGNMENT_EXISTS = "CS180"
__code[SKU_TERMINAL_TYPE_ASSIGNMENT_EXISTS] = {
    "code": SKU_TERMINAL_TYPE_ASSIGNMENT_EXISTS,
    "msg": "SKU-terminal type assignment already exists",
    "status_code": 400,
}

TRANSACTION_MASTER_NOT_FOUND = "CS181"
__code[TRANSACTION_MASTER_NOT_FOUND] = {
    "code": TRANSACTION_MASTER_NOT_FOUND,
    "msg": "Transaction master not found",
    "status_code": 404,
}

TRANSACTION_CODE_ALREADY_EXISTS = "CS182"
__code[TRANSACTION_CODE_ALREADY_EXISTS] = {
    "code": TRANSACTION_CODE_ALREADY_EXISTS,
    "msg": "Transaction code already exists",
    "status_code": 400,
}

SKU_OWNERSHIP_VIOLATION = "CS183"
__code[SKU_OWNERSHIP_VIOLATION] = {
    "code": SKU_OWNERSHIP_VIOLATION,
    "msg": "SKU ownership violation - each property SKU must be exclusively linked to one seller",
    "status_code": 400,
}

TERMINAL_TYPE_SELLER_VIOLATION = "CS184"
__code[TERMINAL_TYPE_SELLER_VIOLATION] = {
    "code": TERMINAL_TYPE_SELLER_VIOLATION,
    "msg": "Terminal type violation - unique terminal type per seller per property required",
    "status_code": 400,
}

PAYMENT_METHOD_NOT_FOUND = "CS185"
__code[PAYMENT_METHOD_NOT_FOUND] = {
    "code": PAYMENT_METHOD_NOT_FOUND,
    "msg": "Payment method not found",
    "status_code": 404,
}

PAYMENT_METHOD_CODE_EXISTS = "CS186"
__code[PAYMENT_METHOD_CODE_EXISTS] = {
    "code": PAYMENT_METHOD_CODE_EXISTS,
    "msg": "Payment method code already exists",
    "status_code": 400,
}

PROPERTY_PAYMENT_METHOD_NOT_FOUND = "CS187"
__code[PROPERTY_PAYMENT_METHOD_NOT_FOUND] = {
    "code": PROPERTY_PAYMENT_METHOD_NOT_FOUND,
    "msg": "Property payment method not found",
    "status_code": 404,
}

PROPERTY_PAYMENT_METHOD_EXISTS = "CS188"
__code[PROPERTY_PAYMENT_METHOD_EXISTS] = {
    "code": PROPERTY_PAYMENT_METHOD_EXISTS,
    "msg": "Property payment method already exists",
    "status_code": 400,
}

TRANSACTION_DEFAULT_MAPPING_EXISTS = "CS189"
__code[TRANSACTION_DEFAULT_MAPPING_EXISTS] = {
    "code": TRANSACTION_DEFAULT_MAPPING_EXISTS,
    "msg": "Transaction default mapping already exists",
    "status_code": 400,
}
