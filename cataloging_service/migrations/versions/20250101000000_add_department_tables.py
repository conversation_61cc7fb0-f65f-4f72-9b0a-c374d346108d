"""Add department management tables

Revision ID: 20250101000000_add_department_tables
Revises: 20241226142839_add_unq_constarint_in_guest_type_property
Create Date: 2025-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250101000000_add_department_tables'
down_revision = '20241226142839_add_unq_constarint_in_guest_type_property'
branch_labels = None
depends_on = None


def upgrade():
    # Create department table
    op.create_table('department',
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('parent_id', sa.Integer(), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('code', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('financial_code', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('auto_create_on_property_launch', sa.Boolean(), nullable=True),
        sa.Column('config', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['parent_id'], ['department.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code')
    )

    # Create brand_department_mapping table
    op.create_table('brand_department_mapping',
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('brand_id', sa.Integer(), nullable=False),
        sa.Column('department_id', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['brand_id'], ['brand.id'], ),
        sa.ForeignKeyConstraint(['department_id'], ['department.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('brand_id', 'department_id')
    )
    op.create_index('idx_brand_department_brand_id', 'brand_department_mapping', ['brand_id'], unique=False)
    op.create_index('idx_brand_department_dept_id', 'brand_department_mapping', ['department_id'], unique=False)

    # Create property_department table
    op.create_table('property_department',
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('property_id', sa.String(), nullable=False),
        sa.Column('tenant_department_id', sa.Integer(), nullable=True),
        sa.Column('inherited_from_brand_id', sa.Integer(), nullable=True),
        sa.Column('parent_id', sa.Integer(), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('code', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('financial_code', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_custom', sa.Boolean(), nullable=True),
        sa.Column('config', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['inherited_from_brand_id'], ['brand.id'], ),
        sa.ForeignKeyConstraint(['parent_id'], ['property_department.id'], ),
        sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
        sa.ForeignKeyConstraint(['tenant_department_id'], ['department.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('property_id', 'code')
    )
    op.create_index('idx_property_department_property_id', 'property_department', ['property_id'], unique=False)
    op.create_index('idx_property_department_tenant_dept', 'property_department', ['tenant_department_id'], unique=False)

    # Create sku_department_mapping table
    op.create_table('sku_department_mapping',
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('sku_id', sa.String(), nullable=False),
        sa.Column('department_id', sa.Integer(), nullable=False),
        sa.Column('effective_from', sa.DateTime(), nullable=True),
        sa.Column('effective_to', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['department_id'], ['department.id'], ),
        sa.ForeignKeyConstraint(['sku_id'], ['sku.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('sku_id', 'department_id')
    )
    op.create_index('idx_sku_department_sku_id', 'sku_department_mapping', ['sku_id'], unique=False)
    op.create_index('idx_sku_department_dept_id', 'sku_department_mapping', ['department_id'], unique=False)

    # Create property_sku_department table
    op.create_table('property_sku_department',
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('property_id', sa.String(), nullable=False),
        sa.Column('sku_id', sa.String(), nullable=False),
        sa.Column('property_department_id', sa.Integer(), nullable=False),
        sa.Column('effective_from', sa.DateTime(), nullable=True),
        sa.Column('effective_to', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['property_department_id'], ['property_department.id'], ),
        sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
        sa.ForeignKeyConstraint(['sku_id'], ['sku.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('property_id', 'sku_id', 'property_department_id')
    )
    op.create_index('idx_prop_sku_dept_property_id', 'property_sku_department', ['property_id'], unique=False)
    op.create_index('idx_prop_sku_dept_sku_id', 'property_sku_department', ['sku_id'], unique=False)
    op.create_index('idx_prop_sku_dept_dept_id', 'property_sku_department', ['property_department_id'], unique=False)

    # Create seller_template table (Terminal Type Template)
    op.create_table('seller_template',
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('code', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('default_department_id', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('auto_create_on_property_launch', sa.Boolean(), nullable=True),
        sa.Column('template_config', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['default_department_id'], ['department.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code')
    )

    # Create sku_terminal_type_assignment table
    op.create_table('sku_terminal_type_assignment',
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('sku_id', sa.String(), nullable=False),
        sa.Column('terminal_type_template_id', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['sku_id'], ['sku.id'], ),
        sa.ForeignKeyConstraint(['terminal_type_template_id'], ['seller_template.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('sku_id', 'terminal_type_template_id')
    )
    op.create_index('idx_sku_terminal_type_sku_id', 'sku_terminal_type_assignment', ['sku_id'], unique=False)
    op.create_index('idx_sku_terminal_type_template_id', 'sku_terminal_type_assignment', ['terminal_type_template_id'], unique=False)

    # Create transaction_master table
    op.create_table('transaction_master',
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('transaction_code', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('entity_type', sa.String(length=50), nullable=False),
        sa.Column('transaction_type', sa.String(length=50), nullable=False),
        sa.Column('operational_unit_id', sa.String(), nullable=True),
        sa.Column('operational_unit_type', sa.String(length=50), nullable=True),
        sa.Column('source', sa.String(length=100), nullable=True),
        sa.Column('gl_code', sa.String(length=20), nullable=True),
        sa.Column('erp_id', sa.String(length=50), nullable=True),
        sa.Column('is_merge', sa.Boolean(), nullable=True),
        sa.Column('particulars', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('hotel_entity_id', sa.String(), nullable=True),
        sa.Column('franchiser_entity_id', sa.String(), nullable=True),
        sa.Column('usali_code', sa.String(length=20), nullable=True),
        sa.Column('usali_category', sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('transaction_code')
    )
    op.create_index('idx_transaction_master_code', 'transaction_master', ['transaction_code'], unique=False)
    op.create_index('idx_transaction_master_entity_type', 'transaction_master', ['entity_type'], unique=False)
    op.create_index('idx_transaction_master_operational_unit', 'transaction_master', ['operational_unit_id', 'operational_unit_type'], unique=False)
    op.create_index('idx_transaction_master_status', 'transaction_master', ['status'], unique=False)

    # Add new fields to seller table
    op.add_column('seller', sa.Column('default_department_id', sa.Integer(), nullable=True))
    op.add_column('seller', sa.Column('property_terminal_id', sa.Integer(), nullable=True))
    op.add_column('seller', sa.Column('is_auto_created', sa.Boolean(), nullable=True))
    op.create_foreign_key('fk_seller_default_department', 'seller', 'property_department', ['default_department_id'], ['id'])
    op.create_foreign_key('fk_seller_terminal_type', 'seller', 'seller_template', ['property_terminal_id'], ['id'])

    # Add seller_id to property_sku table
    op.add_column('property_sku', sa.Column('seller_id', sa.String(), nullable=True))
    op.create_foreign_key('fk_property_sku_seller', 'property_sku', 'seller', ['seller_id'], ['seller_id'])
    op.create_unique_constraint('uq_property_sku_seller_id', 'property_sku', ['seller_id'])


def downgrade():
    # Remove seller_id from property_sku table
    op.drop_constraint('uq_property_sku_seller_id', 'property_sku', type_='unique')
    op.drop_constraint('fk_property_sku_seller', 'property_sku', type_='foreignkey')
    op.drop_column('property_sku', 'seller_id')

    # Remove new fields from seller table
    op.drop_constraint('fk_seller_terminal_type', 'seller', type_='foreignkey')
    op.drop_constraint('fk_seller_default_department', 'seller', type_='foreignkey')
    op.drop_column('seller', 'is_auto_created')
    op.drop_column('seller', 'property_terminal_id')
    op.drop_column('seller', 'default_department_id')

    # Drop transaction_master table
    op.drop_index('idx_transaction_master_status', table_name='transaction_master')
    op.drop_index('idx_transaction_master_operational_unit', table_name='transaction_master')
    op.drop_index('idx_transaction_master_entity_type', table_name='transaction_master')
    op.drop_index('idx_transaction_master_code', table_name='transaction_master')
    op.drop_table('transaction_master')

    # Drop sku_terminal_type_assignment table
    op.drop_index('idx_sku_terminal_type_template_id', table_name='sku_terminal_type_assignment')
    op.drop_index('idx_sku_terminal_type_sku_id', table_name='sku_terminal_type_assignment')
    op.drop_table('sku_terminal_type_assignment')

    # Drop tables in reverse order
    op.drop_table('seller_template')

    op.drop_index('idx_prop_sku_dept_dept_id', table_name='property_sku_department')
    op.drop_index('idx_prop_sku_dept_sku_id', table_name='property_sku_department')
    op.drop_index('idx_prop_sku_dept_property_id', table_name='property_sku_department')
    op.drop_table('property_sku_department')

    op.drop_index('idx_sku_department_dept_id', table_name='sku_department_mapping')
    op.drop_index('idx_sku_department_sku_id', table_name='sku_department_mapping')
    op.drop_table('sku_department_mapping')

    op.drop_index('idx_property_department_tenant_dept', table_name='property_department')
    op.drop_index('idx_property_department_property_id', table_name='property_department')
    op.drop_table('property_department')

    op.drop_index('idx_brand_department_dept_id', table_name='brand_department_mapping')
    op.drop_index('idx_brand_department_brand_id', table_name='brand_department_mapping')
    op.drop_table('brand_department_mapping')

    op.drop_table('department')
