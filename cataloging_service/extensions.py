from flask_admin import Admin
from flask_apispec import FlaskApiSpec
from flask_marshmallow import Marshmallow
from flask_security.core import Security

from cataloging_service.constants import constants
from cataloging_service.infrastructure import redis_cache
from core.common import extended_json

ma = Marshmallow()
admin = Admin(name=constants.ADMIN_NAME, template_mode=constants.ADMIN_TEMPLATE, url=constants.ADMIN_URL)
security = Security()
cache = redis_cache.get_cache()
api_docs = FlaskApiSpec()
extended_json.patch_json()
