import logging
import os
import traceback
from inspect import getmembers

import click
from flask import Flask, jsonify, Blueprint, url_for
from flask_security import SQLAlchemySessionUserDatastore
from sqlalchemy import exc

from healthcheck import HealthCheck
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.request_tracing.flask.after_request import clear_request_context
from werkzeug.exceptions import HTTPException
from werkzeug.utils import redirect
from whitenoise import WhiteNoise

from cataloging_service.admin.admin_views import get_admin_views
from cataloging_service.api.tenant_config import tenant_config_bp
from cataloging_service.api.tenant_config_v2 import tenant_config_v2_bp
from cataloging_service.api.currency_conversion_rates import currency_conv_rates_bp
from cataloging_service.api.user_defined_enums import user_defined_enum_bp
from cataloging_service.audit_extension import audit_ext
from cataloging_service.db_listeners import init_listeners
from cataloging_service.domain.common.audit.audit import attach_auditing
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.extensions import ma, admin, security, api_docs
from cataloging_service.infrastructure import redis_cache
from cataloging_service.models import User, Role

config = {
    "development": "cataloging_service.settings.DevelopmentConfig",
    "testing": "cataloging_service.settings.TestingConfig",
    "default": "cataloging_service.settings.DevelopmentConfig",
    "production": "cataloging_service.settings.ProductionConfig",
    "staging": "cataloging_service.settings.StagingConfig"
}

logger = logging.getLogger(__name__)


def create_app():
    click.echo("Creating App with name: %s" % __name__)

    tmpl_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    click.echo("Template directory is at {0}".format(tmpl_dir))
    app = Flask(__name__, instance_relative_config=True,
                instance_path=os.environ.get('FLASK_APP_INSTANCE_PATH'),
                template_folder=tmpl_dir, static_url_path="/cataloging-service/static")
    app.url_map.strict_slashes = False
    setup_config(app)
    app.before_request_funcs = {None: app.config['BEFORE_REQUEST_MIDDLEWARES']}
    app.after_request_funcs = {None: app.config['AFTER_REQUEST_MIDDLEWARES']}
    register_extensions(app)
    register_blueprints(app)
    setup_admin(app)
    setup_whitenoise(app)
    setup_caching(app)
    health_check_cm(app)
    generalise_exception(app)
    init_listeners()
    setup_fav_icon(app)
    setup_swagger(app)
    setup_auto_version(app)
    attach_auditing()
    app.jinja_env.add_extension('jinja2.ext.do')

    @app.teardown_request
    def shutdown_session(exception=None):
        if os.environ.get('APP_ENV') != 'testing':
            db_engine.remove_session()

    @app.teardown_appcontext
    def clear_thread_local(exception=None):
        try:
            logger.info("Connection pool status: %s", db_engine.get_engine(get_current_tenant_id()).pool.status())
        except:
            logger.error("Can't log connection pool status")
            pass

        if os.environ.get('APP_ENV') == 'testing':
            db_engine.remove_session()

        clear_request_context()
    return app


def setup_admin(app):
    admin.init_app(app)
    tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())
    views = get_admin_views(tenant_id=tenant_id)
    for view in views:
        admin.add_views(view)

    data_store = SQLAlchemySessionUserDatastore(db_engine.get_session(tenant_id=tenant_id), User, Role)
    security.init_app(app, data_store)


def setup_config(app):
    environment = os.environ.get('APP_ENV', 'development')
    click.echo("Using environment: %s" % environment)
    app.config.from_object(config[environment])
    app.config.from_pyfile('cataloging-service.cfg', silent=True)
    configure_logging(app)


def configure_logging(app):
    import logging.config
    environment = os.environ.get('APP_ENV', 'development')

    LOGGING = {
        'version': 1,
        'filters': {
            'request_id': {
                '()': 'treebo_commons.request_tracing.log_filters.RequestContextFilter',
            },
        },
        'disable_existing_loggers': False,
        'formatters': {
            'verbose': {
                'format': "[%(asctime)s] %(levelname)s %(request_id)s [%(name)s:%(lineno)s] %(message)s"
            },
            'logstash': {
                '()': 'logstash_formatter.LogstashFormatterV1'
            },
        },
        'handlers': {
            'null': {
                'level': 'INFO',
                'class': 'logging.NullHandler',
                'filters': ['request_id'],
            },
            'console': {
                'level': 'INFO',
                'class': 'logging.StreamHandler',
                'formatter': 'logstash',
                'filters': ['request_id'],
            },
            'cs': {
                'level': 'DEBUG',
                'class': 'logging.StreamHandler',
                'formatter': 'logstash' if environment in ['production', 'staging', 'development'] else 'verbose',
                'filters': ['request_id'],
            },
        },
        'loggers': {
            'cataloging_service': {
                'handlers': ['cs'],
                'level': 'ERROR' if environment == 'production' else 'DEBUG',
                'propagate': True,
            },
            'error': {
                'handlers': ['cs'],
                'level': 'ERROR',
                'propagate': True,
            },
            'werkzeug': {
                'handlers': ['cs'],
                'level': 'ERROR',
                'propagate': True,
            },
            '': {
                'handlers': ['console', 'cs'],
                'level': 'ERROR' if environment == 'production' else 'INFO',
            },
        },
    }
    logging.config.dictConfig(LOGGING)


def register_extensions(app):
    ma.init_app(app)
    audit_ext.init_app(app)


def setup_whitenoise(app):
    environment = os.environ.get('APP_ENV', 'development')
    autorefresh = environment in ('development', 'staging', 'local')
    app.wsgi_app = WhiteNoise(app.wsgi_app, root=app.static_folder, prefix=app.static_url_path, autorefresh=autorefresh)


def setup_caching(app):
    redis_cache.init_app(app)


def health_check_cm(app):
    health = HealthCheck(app, '/cataloging-service/health/', ['rds'], [])

    def rds_available():
        for tenant_id, scoped_session in db_engine.tenant_wise_sessions.items():
            Session = scoped_session()
            try:
                logger.info("Making connection with RDS for tenant_id {0}".format(tenant_id))
                Session.execute('SELECT 1')
                logger.info("Connection successful with RDS")
            except Exception as e:
                logger.error('Exception occured while connection with RDS %s' % e)
                raise e
            finally:
                scoped_session.remove()
        return True, "connection successful"

    def rabbitmq_available():
        from kombu import Connection
        from amqp.exceptions import ConnectionError
        try:
            for tenant in TenantClient.get_active_tenants():
                rmq_url = AwsSecretManager.get_rmq_url(tenant_id=tenant.tenant_id)
                conn = Connection(rmq_url, transport_options={'confirm_publish': True})
                try:
                    conn.connect()
                    if not conn.connected:
                        logger.error('Connection with rabbitmq failed. TenantId: %s, RMQ URL: %s', tenant.tenant_id,
                                     rmq_url)
                        raise ConnectionError()
                except Exception as e:
                    logger.error('Connection with rabbitmq failed. TenantId: %s, RMQ URL: %s', tenant.tenant_id,
                                 rmq_url)
                    raise

                conn.release()
                logger.info("Connection successful with Rabbitmq for tenant_id: %s", tenant.tenant_id)
        except Exception as e:
            raise ConnectionError()

        return True, "connection successful"

    health.add_check(rds_available)
    health.add_check(rabbitmq_available)


def handle_error(error):
    logging.error(traceback.format_exc())
    code = 500
    if type(error) == CatalogingServiceException:
        response = {'status': 'error', 'message': error.get_error_message()}
        reason = error.get_error_context()
        if reason:
            response['reason'] = reason
        response = jsonify(response)
        response.status_code = error.get_response_code()
        return response
    elif isinstance(error, exc.IntegrityError):
        response = {'status': 'error', 'message': "Invalid request data"}
        reason = error.orig.args
        if reason:
            response['reason'] = reason
        response = jsonify(response)
        response.status_code = 400
        return response
    elif isinstance(error, HTTPException):
        code = error.code
        return jsonify(error=repr(error), code=code)
    else:
        return jsonify(error=repr(error), code=code)


def generalise_exception(app):
    """
    Controversial though, but the safest bet here! CRS also uses similar kind of implementation
    :param app:
    :return:
    """
    for cls in HTTPException.__subclasses__():
        app.register_error_handler(cls, handle_error)
    app.register_error_handler(Exception, handle_error)


def register_blueprints(app):
    from cataloging_service import blueprints
    from cataloging_service.blueprints import all_old_api_bps, all_old_bps, all_old_script_bps, all_new_api_bps

    all_bps = [func for _, func in getmembers(blueprints) if isinstance(func, Blueprint)]

    [app.register_blueprint(bp, url_prefix='/cataloging-service') for bp in all_old_bps]
    [app.register_blueprint(bp, url_prefix='/cataloging-service/api') for bp in all_old_api_bps]
    [app.register_blueprint(bp, url_prefix='/cataloging-service/scripts') for bp in all_old_script_bps]
    [app.register_blueprint(bp) for bp in all_new_api_bps]
    app.register_blueprint(tenant_config_bp, url_prefix='/cataloging-service/api' + tenant_config_bp.url_prefix)
    app.register_blueprint(tenant_config_v2_bp, url_prefix='/cataloging-service/api' + tenant_config_v2_bp.url_prefix)
    app.register_blueprint(user_defined_enum_bp, url_prefix='/cataloging-service/api' + user_defined_enum_bp.url_prefix)
    app.register_blueprint(currency_conv_rates_bp,
                           url_prefix='/cataloging-service/api' + currency_conv_rates_bp.url_prefix)


def setup_swagger(app):
    """
    Uses a mix of apispec, apispect-webframework, FlaskApiSpec

    apispec - to generate schema and marshmallow schema -> openapi spec
    apispect-webframework - For converting flask views as openapi path
    flask-apispec - For providing swagger assets and swagger views
    """
    from cataloging_service.apispecs import spec, schemas, views
    app.config.update({
        'APISPEC_SPEC': spec,
        'APISPEC_SWAGGER_URL': '/api/spec',
        'APISPEC_SWAGGER_UI_URL': '/api/docs',
    })
    [spec.components.schema(sch.__name__, schema=sch) for sch in schemas]

    with app.app_context():
        [spec.path(view=vw) for vw in views]

    api_docs.init_app(app)


def setup_fav_icon(app):
    @app.route('/favicon.ico')
    def favicon():
        return redirect(url_for('static', filename='favicon.ico'), code=301)


def setup_auto_version(app):
    @app.template_filter('autoversion')
    def autoversion_filter(filename):
        _fn = filename.replace(app.static_url_path, '')
        try:
            timestamp = str(os.path.getmtime(app.static_folder + _fn))
            _new_fn = "{0}?v={1}".format(_fn, timestamp)
            return filename.replace(_fn, _new_fn)
        except OSError:
            pass

        return filename
