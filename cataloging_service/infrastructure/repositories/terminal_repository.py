from sqlalchemy.orm import joinedload
from sqlalchemy.exc import IntegrityError
from cataloging_service.models import SellerTemplate, SkuTerminalTypeAssignment
from cataloging_service.infrastructure.repositories.repository import BaseRepository
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class TerminalRepository(BaseRepository):
    """Repository for terminal/seller template operations"""

    # Seller Template (Terminal Type) operations
    def get_all_seller_templates(self, include_inactive=False):
        """Get all seller templates"""
        query = self.session.query(SellerTemplate).options(
            joinedload(SellerTemplate.default_department)
        )
        if not include_inactive:
            query = query.filter(SellerTemplate.is_active == True)
        return query.order_by(SellerTemplate.name).all()

    def get_seller_template_by_id(self, template_id):
        """Get seller template by ID"""
        return self.session.query(SellerTemplate).options(
            joinedload(SellerTemplate.default_department)
        ).filter(SellerTemplate.id == template_id).first()

    def get_seller_template_by_code(self, code):
        """Get seller template by code"""
        return self.session.query(SellerTemplate).options(
            joinedload(SellerTemplate.default_department)
        ).filter(SellerTemplate.code == code).first()

    def create_seller_template(self, template_data):
        """Create seller template"""
        try:
            template = SellerTemplate(**template_data)
            self.session.add(template)
            self.session.commit()
            return template
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_CODE_EXISTS)

    def update_seller_template(self, template_id, update_data):
        """Update seller template"""
        template = self.get_seller_template_by_id(template_id)
        if not template:
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(template, key, value)
            self.session.commit()
            return template
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_CODE_EXISTS)

    # SKU Terminal Type Assignment operations
    def get_sku_terminal_type_assignments(self, sku_id):
        """Get terminal type assignments for a SKU"""
        return self.session.query(SkuTerminalTypeAssignment).options(
            joinedload(SkuTerminalTypeAssignment.terminal_type_template)
        ).filter(
            SkuTerminalTypeAssignment.sku_id == sku_id,
            SkuTerminalTypeAssignment.is_active == True
        ).all()

    def create_sku_terminal_type_assignment(self, sku_id, terminal_type_template_id):
        """Create SKU-terminal type assignment"""
        try:
            assignment = SkuTerminalTypeAssignment(
                sku_id=sku_id,
                terminal_type_template_id=terminal_type_template_id,
                is_active=True
            )
            self.session.add(assignment)
            self.session.commit()
            return assignment
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.SKU_TERMINAL_TYPE_ASSIGNMENT_EXISTS)

    def remove_sku_terminal_type_assignment(self, sku_id, terminal_type_template_id):
        """Remove SKU-terminal type assignment"""
        assignment = self.session.query(SkuTerminalTypeAssignment).filter(
            SkuTerminalTypeAssignment.sku_id == sku_id,
            SkuTerminalTypeAssignment.terminal_type_template_id == terminal_type_template_id
        ).first()

        if assignment:
            assignment.is_active = False
            self.session.commit()
        return assignment

    def get_terminal_types_for_sku(self, sku_id):
        """Get all terminal types that can sell a specific SKU"""
        return self.session.query(SellerTemplate).join(
            SkuTerminalTypeAssignment,
            SellerTemplate.id == SkuTerminalTypeAssignment.terminal_type_template_id
        ).filter(
            SkuTerminalTypeAssignment.sku_id == sku_id,
            SkuTerminalTypeAssignment.is_active == True,
            SellerTemplate.is_active == True
        ).all()

    def get_skus_for_terminal_type(self, terminal_type_id):
        """Get all SKUs assigned to a specific terminal type"""
        from cataloging_service.models import Sku
        return self.session.query(Sku).join(
            SkuTerminalTypeAssignment,
            Sku.id == SkuTerminalTypeAssignment.sku_id
        ).filter(
            SkuTerminalTypeAssignment.terminal_type_template_id == terminal_type_id,
            SkuTerminalTypeAssignment.is_active == True
        ).all()

    def validate_terminal_type_seller_relation(self, property_id, seller_id, terminal_type_id):
        """Validate unique terminal type per seller per property"""
        from cataloging_service.models import Seller
        existing_seller = self.session.query(Seller).filter(
            Seller.property_id == property_id,
            Seller.property_terminal_id == terminal_type_id,
            Seller.seller_id != seller_id
        ).first()

        if existing_seller:
            raise CatalogingServiceException(error_codes.TERMINAL_TYPE_SELLER_VIOLATION)

        return True
