from sqlalchemy.orm import joinedload
from sqlalchemy.exc import IntegrityError
from cataloging_service.models import TransactionMaster, TransactionDefaultMapping
from cataloging_service.infrastructure.repositories.repository import BaseRepository
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class FinancialRepository(BaseRepository):
    """Repository for financial operations - Transaction Master and Default Mappings"""

    # Transaction Master operations
    def get_all_transaction_masters(self, entity_type=None, status=None):
        """Get all transaction masters with optional filtering"""
        query = self.session.query(TransactionMaster)
        if entity_type:
            query = query.filter(TransactionMaster.entity_type == entity_type)
        if status:
            query = query.filter(TransactionMaster.status == status)
        return query.order_by(TransactionMaster.transaction_code).all()

    def get_transaction_master_by_id(self, transaction_id):
        """Get transaction master by ID"""
        return self.session.query(TransactionMaster).filter(TransactionMaster.id == transaction_id).first()

    def get_transaction_master_by_code(self, transaction_code):
        """Get transaction master by code"""
        return self.session.query(TransactionMaster).filter(TransactionMaster.transaction_code == transaction_code).first()

    def create_transaction_master(self, transaction_data):
        """Create transaction master"""
        try:
            transaction = TransactionMaster(**transaction_data)
            self.session.add(transaction)
            self.session.commit()
            return transaction
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.TRANSACTION_CODE_ALREADY_EXISTS)

    def update_transaction_master(self, transaction_id, update_data):
        """Update transaction master"""
        transaction = self.get_transaction_master_by_id(transaction_id)
        if not transaction:
            raise CatalogingServiceException(error_codes.TRANSACTION_MASTER_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(transaction, key, value)
            self.session.commit()
            return transaction
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.TRANSACTION_CODE_ALREADY_EXISTS)

    def generate_transaction_code(self, entity_type, entity_id):
        """Generate unique transaction code"""
        # Simple implementation - can be enhanced with more sophisticated logic
        prefix_map = {
            'SKU': 'SKU',
            'PaymentMethod': 'PAY',
            'Department': 'DEPT',
            'Property': 'PROP'
        }
        prefix = prefix_map.get(entity_type, 'TXN')

        # Find the next available number
        existing_codes = self.session.query(TransactionMaster.transaction_code).filter(
            TransactionMaster.transaction_code.like(f'{prefix}%')
        ).all()

        max_num = 0
        for code_tuple in existing_codes:
            code = code_tuple[0]
            try:
                num = int(code.replace(prefix, ''))
                max_num = max(max_num, num)
            except ValueError:
                continue

        return f"{prefix}{max_num + 1:06d}"

    # Transaction Default Mapping operations
    def get_transaction_default_mappings(self, entity_type=None):
        """Get transaction default mappings"""
        query = self.session.query(TransactionDefaultMapping)
        if entity_type:
            query = query.filter(TransactionDefaultMapping.triggering_entity_type == entity_type)
        return query.filter(TransactionDefaultMapping.is_active == True).all()

    def create_transaction_default_mapping(self, mapping_data):
        """Create transaction default mapping"""
        try:
            mapping = TransactionDefaultMapping(**mapping_data)
            self.session.add(mapping)
            self.session.commit()
            return mapping
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.TRANSACTION_DEFAULT_MAPPING_EXISTS)

    def get_default_mapping_for_entity(self, entity_type, entity_category, transaction_entity_type):
        """Get default mapping for specific entity"""
        return self.session.query(TransactionDefaultMapping).filter(
            TransactionDefaultMapping.triggering_entity_type == entity_type,
            TransactionDefaultMapping.triggering_entity_category == entity_category,
            TransactionDefaultMapping.entity_type == transaction_entity_type,
            TransactionDefaultMapping.is_active == True
        ).first()
