from sqlalchemy.orm import joinedload
from sqlalchemy.exc import IntegrityError
from cataloging_service.models import (
    Department, BrandDepartmentMapping, PropertyDepartment,
    SkuDepartmentMapping, PropertySkuDepartment
)
from cataloging_service.infrastructure.repositories.repository import BaseRepository
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class DepartmentRepository(BaseRepository):
    """Repository for department management operations"""

    def __init__(self):
        super().__init__()

    # Department CRUD operations
    def get_all_departments(self, include_inactive=False):
        """Get all tenant-level departments"""
        query = self.session.query(Department)
        if not include_inactive:
            query = query.filter(Department.is_active == True)
        return query.order_by(Department.name).all()

    def get_department_by_id(self, department_id):
        """Get department by ID"""
        return self.session.query(Department).filter(Department.id == department_id).first()

    def get_department_by_code(self, code):
        """Get department by code"""
        return self.session.query(Department).filter(Department.code == code).first()

    def get_departments_hierarchy(self, parent_id=None):
        """Get departments in hierarchical structure"""
        query = self.session.query(Department).filter(Department.is_active == True)
        if parent_id is None:
            query = query.filter(Department.parent_id.is_(None))
        else:
            query = query.filter(Department.parent_id == parent_id)
        return query.order_by(Department.name).all()

    def create_department(self, department_data):
        """Create new department"""
        try:
            department = Department(**department_data)
            self.session.add(department)
            self.session.commit()
            return department
        except IntegrityError as e:
            self.session.rollback()
            logger.error(f"Error creating department: {e}")
            raise CatalogingServiceException(error_codes.DEPARTMENT_CODE_ALREADY_EXISTS)

    def update_department(self, department_id, update_data):
        """Update department"""
        department = self.get_department_by_id(department_id)
        if not department:
            raise CatalogingServiceException(error_codes.DEPARTMENT_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(department, key, value)
            self.session.commit()
            return department
        except IntegrityError as e:
            self.session.rollback()
            logger.error(f"Error updating department: {e}")
            raise CatalogingServiceException(error_codes.DEPARTMENT_CODE_ALREADY_EXISTS)

    def delete_department(self, department_id):
        """Soft delete department"""
        department = self.get_department_by_id(department_id)
        if not department:
            raise CatalogingServiceException(error_codes.DEPARTMENT_NOT_FOUND)

        # Check if department has children
        children = self.session.query(Department).filter(Department.parent_id == department_id).count()
        if children > 0:
            raise CatalogingServiceException(error_codes.DEPARTMENT_HAS_CHILDREN)

        department.is_active = False
        self.session.commit()
        return department

    # Brand Department Mapping operations
    def get_brand_departments(self, brand_id):
        """Get all departments mapped to a brand"""
        return self.session.query(BrandDepartmentMapping).options(
            joinedload(BrandDepartmentMapping.department)
        ).filter(
            BrandDepartmentMapping.brand_id == brand_id,
            BrandDepartmentMapping.is_active == True
        ).all()

    def create_brand_department_mapping(self, brand_id, department_id):
        """Create brand-department mapping"""
        try:
            mapping = BrandDepartmentMapping(
                brand_id=brand_id,
                department_id=department_id,
                is_active=True
            )
            self.session.add(mapping)
            self.session.commit()
            return mapping
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.BRAND_DEPARTMENT_MAPPING_EXISTS)

    def remove_brand_department_mapping(self, brand_id, department_id):
        """Remove brand-department mapping"""
        mapping = self.session.query(BrandDepartmentMapping).filter(
            BrandDepartmentMapping.brand_id == brand_id,
            BrandDepartmentMapping.department_id == department_id
        ).first()

        if mapping:
            mapping.is_active = False
            self.session.commit()
        return mapping

    # Property Department operations
    def get_property_departments(self, property_id, include_inactive=False):
        """Get all departments for a property"""
        query = self.session.query(PropertyDepartment).filter(
            PropertyDepartment.property_id == property_id
        )
        if not include_inactive:
            query = query.filter(PropertyDepartment.is_active == True)
        return query.order_by(PropertyDepartment.name).all()

    def get_property_department_by_id(self, property_department_id):
        """Get property department by ID"""
        return self.session.query(PropertyDepartment).filter(
            PropertyDepartment.id == property_department_id
        ).first()

    def get_property_department_by_code(self, property_id, code):
        """Get property department by code"""
        return self.session.query(PropertyDepartment).filter(
            PropertyDepartment.property_id == property_id,
            PropertyDepartment.code == code
        ).first()

    def create_property_department(self, property_department_data):
        """Create property department"""
        try:
            property_department = PropertyDepartment(**property_department_data)
            self.session.add(property_department)
            self.session.commit()
            return property_department
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_CODE_EXISTS)

    def update_property_department(self, property_department_id, update_data):
        """Update property department"""
        property_department = self.get_property_department_by_id(property_department_id)
        if not property_department:
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(property_department, key, value)
            self.session.commit()
            return property_department
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_CODE_EXISTS)

    # SKU Department Mapping operations
    def get_sku_departments(self, sku_id):
        """Get departments mapped to a SKU"""
        return self.session.query(SkuDepartmentMapping).options(
            joinedload(SkuDepartmentMapping.department)
        ).filter(
            SkuDepartmentMapping.sku_id == sku_id,
            SkuDepartmentMapping.is_active == True
        ).all()

    def create_sku_department_mapping(self, sku_id, department_id):
        """Create SKU-department mapping"""
        try:
            mapping = SkuDepartmentMapping(
                sku_id=sku_id,
                department_id=department_id,
                is_active=True
            )
            self.session.add(mapping)
            self.session.commit()
            return mapping
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.SKU_DEPARTMENT_MAPPING_EXISTS)

    # Property SKU Department operations
    def get_property_sku_departments(self, property_id, sku_id=None):
        """Get property-SKU-department mappings"""
        query = self.session.query(PropertySkuDepartment).filter(
            PropertySkuDepartment.property_id == property_id,
            PropertySkuDepartment.is_active == True
        )
        if sku_id:
            query = query.filter(PropertySkuDepartment.sku_id == sku_id)
        return query.all()

    def create_property_sku_department_mapping(self, property_id, sku_id, property_department_id):
        """Create property-SKU-department mapping"""
        try:
            mapping = PropertySkuDepartment(
                property_id=property_id,
                sku_id=sku_id,
                property_department_id=property_department_id,
                is_active=True
            )
            self.session.add(mapping)
            self.session.commit()
            return mapping
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PROPERTY_SKU_DEPARTMENT_MAPPING_EXISTS)

    def validate_sku_ownership(self, property_id, sku_id, seller_id):
        """Validate SKU ownership rules"""
        # Check if SKU is already owned by another seller
        from cataloging_service.models import PropertySku
        existing_property_sku = self.session.query(PropertySku).filter(
            PropertySku.property_id == property_id,
            PropertySku.sku_id == sku_id,
            PropertySku.seller_id.isnot(None),
            PropertySku.seller_id != seller_id
        ).first()

        if existing_property_sku:
            raise CatalogingServiceException(error_codes.SKU_OWNERSHIP_VIOLATION)

        return True
