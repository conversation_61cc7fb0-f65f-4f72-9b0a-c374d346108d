from sqlalchemy.orm import joinedload
from sqlalchemy.exc import IntegrityError
from cataloging_service.models import (
    Department, BrandDepartmentMapping, PropertyDepartment,
    SkuDepartmentMapping, PropertySkuDepartment, SellerTemplate,
    SkuTerminalTypeAssignment, TransactionMaster
)
from cataloging_service.infrastructure.repositories.repository import BaseRepository
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class DepartmentRepository(BaseRepository):
    """Repository for department management operations"""

    def __init__(self):
        super().__init__()

    # Department CRUD operations
    def get_all_departments(self, include_inactive=False):
        """Get all tenant-level departments"""
        query = self.session.query(Department)
        if not include_inactive:
            query = query.filter(Department.is_active == True)
        return query.order_by(Department.name).all()

    def get_department_by_id(self, department_id):
        """Get department by ID"""
        return self.session.query(Department).filter(Department.id == department_id).first()

    def get_department_by_code(self, code):
        """Get department by code"""
        return self.session.query(Department).filter(Department.code == code).first()

    def get_departments_hierarchy(self, parent_id=None):
        """Get departments in hierarchical structure"""
        query = self.session.query(Department).filter(Department.is_active == True)
        if parent_id is None:
            query = query.filter(Department.parent_id.is_(None))
        else:
            query = query.filter(Department.parent_id == parent_id)
        return query.order_by(Department.name).all()

    def create_department(self, department_data):
        """Create new department"""
        try:
            department = Department(**department_data)
            self.session.add(department)
            self.session.commit()
            return department
        except IntegrityError as e:
            self.session.rollback()
            logger.error(f"Error creating department: {e}")
            raise CatalogingServiceException(error_codes.DEPARTMENT_CODE_ALREADY_EXISTS)

    def update_department(self, department_id, update_data):
        """Update department"""
        department = self.get_department_by_id(department_id)
        if not department:
            raise CatalogingServiceException(error_codes.DEPARTMENT_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(department, key, value)
            self.session.commit()
            return department
        except IntegrityError as e:
            self.session.rollback()
            logger.error(f"Error updating department: {e}")
            raise CatalogingServiceException(error_codes.DEPARTMENT_CODE_ALREADY_EXISTS)

    def delete_department(self, department_id):
        """Soft delete department"""
        department = self.get_department_by_id(department_id)
        if not department:
            raise CatalogingServiceException(error_codes.DEPARTMENT_NOT_FOUND)

        # Check if department has children
        children = self.session.query(Department).filter(Department.parent_id == department_id).count()
        if children > 0:
            raise CatalogingServiceException(error_codes.DEPARTMENT_HAS_CHILDREN)

        department.is_active = False
        self.session.commit()
        return department

    # Brand Department Mapping operations
    def get_brand_departments(self, brand_id):
        """Get all departments mapped to a brand"""
        return self.session.query(BrandDepartmentMapping).options(
            joinedload(BrandDepartmentMapping.department)
        ).filter(
            BrandDepartmentMapping.brand_id == brand_id,
            BrandDepartmentMapping.is_active == True
        ).all()

    def create_brand_department_mapping(self, brand_id, department_id):
        """Create brand-department mapping"""
        try:
            mapping = BrandDepartmentMapping(
                brand_id=brand_id,
                department_id=department_id,
                is_active=True
            )
            self.session.add(mapping)
            self.session.commit()
            return mapping
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.BRAND_DEPARTMENT_MAPPING_EXISTS)

    def remove_brand_department_mapping(self, brand_id, department_id):
        """Remove brand-department mapping"""
        mapping = self.session.query(BrandDepartmentMapping).filter(
            BrandDepartmentMapping.brand_id == brand_id,
            BrandDepartmentMapping.department_id == department_id
        ).first()

        if mapping:
            mapping.is_active = False
            self.session.commit()
        return mapping

    # Property Department operations
    def get_property_departments(self, property_id, include_inactive=False):
        """Get all departments for a property"""
        query = self.session.query(PropertyDepartment).filter(
            PropertyDepartment.property_id == property_id
        )
        if not include_inactive:
            query = query.filter(PropertyDepartment.is_active == True)
        return query.order_by(PropertyDepartment.name).all()

    def get_property_department_by_id(self, property_department_id):
        """Get property department by ID"""
        return self.session.query(PropertyDepartment).filter(
            PropertyDepartment.id == property_department_id
        ).first()

    def get_property_department_by_code(self, property_id, code):
        """Get property department by code"""
        return self.session.query(PropertyDepartment).filter(
            PropertyDepartment.property_id == property_id,
            PropertyDepartment.code == code
        ).first()

    def create_property_department(self, property_department_data):
        """Create property department"""
        try:
            property_department = PropertyDepartment(**property_department_data)
            self.session.add(property_department)
            self.session.commit()
            return property_department
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_CODE_EXISTS)

    def update_property_department(self, property_department_id, update_data):
        """Update property department"""
        property_department = self.get_property_department_by_id(property_department_id)
        if not property_department:
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(property_department, key, value)
            self.session.commit()
            return property_department
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_CODE_EXISTS)

    # SKU Department Mapping operations
    def get_sku_departments(self, sku_id):
        """Get departments mapped to a SKU"""
        return self.session.query(SkuDepartmentMapping).options(
            joinedload(SkuDepartmentMapping.department)
        ).filter(
            SkuDepartmentMapping.sku_id == sku_id,
            SkuDepartmentMapping.is_active == True
        ).all()

    def create_sku_department_mapping(self, sku_id, department_id):
        """Create SKU-department mapping"""
        try:
            mapping = SkuDepartmentMapping(
                sku_id=sku_id,
                department_id=department_id,
                is_active=True
            )
            self.session.add(mapping)
            self.session.commit()
            return mapping
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.SKU_DEPARTMENT_MAPPING_EXISTS)

    # Property SKU Department operations
    def get_property_sku_departments(self, property_id, sku_id=None):
        """Get property-SKU-department mappings"""
        query = self.session.query(PropertySkuDepartment).filter(
            PropertySkuDepartment.property_id == property_id,
            PropertySkuDepartment.is_active == True
        )
        if sku_id:
            query = query.filter(PropertySkuDepartment.sku_id == sku_id)
        return query.all()

    def create_property_sku_department_mapping(self, property_id, sku_id, property_department_id):
        """Create property-SKU-department mapping"""
        try:
            mapping = PropertySkuDepartment(
                property_id=property_id,
                sku_id=sku_id,
                property_department_id=property_department_id,
                is_active=True
            )
            self.session.add(mapping)
            self.session.commit()
            return mapping
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PROPERTY_SKU_DEPARTMENT_MAPPING_EXISTS)

    # Seller Template operations
    def get_all_seller_templates(self, include_inactive=False):
        """Get all seller templates"""
        query = self.session.query(SellerTemplate)
        if not include_inactive:
            query = query.filter(SellerTemplate.is_active == True)
        return query.order_by(SellerTemplate.name).all()

    def get_seller_template_by_id(self, template_id):
        """Get seller template by ID"""
        return self.session.query(SellerTemplate).filter(SellerTemplate.id == template_id).first()

    def create_seller_template(self, template_data):
        """Create seller template"""
        template = SellerTemplate(**template_data)
        self.session.add(template)
        self.session.commit()
        return template

    def update_seller_template(self, template_id, update_data):
        """Update seller template"""
        template = self.get_seller_template_by_id(template_id)
        if not template:
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_NOT_FOUND)

        for key, value in update_data.items():
            setattr(template, key, value)
        self.session.commit()
        return template

    # SKU Terminal Type Assignment operations
    def get_sku_terminal_type_assignments(self, sku_id):
        """Get terminal type assignments for a SKU"""
        return self.session.query(SkuTerminalTypeAssignment).options(
            joinedload(SkuTerminalTypeAssignment.terminal_type_template)
        ).filter(
            SkuTerminalTypeAssignment.sku_id == sku_id,
            SkuTerminalTypeAssignment.is_active == True
        ).all()

    def create_sku_terminal_type_assignment(self, sku_id, terminal_type_template_id):
        """Create SKU-terminal type assignment"""
        try:
            assignment = SkuTerminalTypeAssignment(
                sku_id=sku_id,
                terminal_type_template_id=terminal_type_template_id,
                is_active=True
            )
            self.session.add(assignment)
            self.session.commit()
            return assignment
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.SKU_TERMINAL_TYPE_ASSIGNMENT_EXISTS)

    def remove_sku_terminal_type_assignment(self, sku_id, terminal_type_template_id):
        """Remove SKU-terminal type assignment"""
        assignment = self.session.query(SkuTerminalTypeAssignment).filter(
            SkuTerminalTypeAssignment.sku_id == sku_id,
            SkuTerminalTypeAssignment.terminal_type_template_id == terminal_type_template_id
        ).first()

        if assignment:
            assignment.is_active = False
            self.session.commit()
        return assignment

    # Transaction Master operations
    def get_all_transaction_masters(self, entity_type=None, status=None):
        """Get all transaction masters with optional filtering"""
        query = self.session.query(TransactionMaster)
        if entity_type:
            query = query.filter(TransactionMaster.entity_type == entity_type)
        if status:
            query = query.filter(TransactionMaster.status == status)
        return query.order_by(TransactionMaster.transaction_code).all()

    def get_transaction_master_by_id(self, transaction_id):
        """Get transaction master by ID"""
        return self.session.query(TransactionMaster).filter(TransactionMaster.id == transaction_id).first()

    def get_transaction_master_by_code(self, transaction_code):
        """Get transaction master by code"""
        return self.session.query(TransactionMaster).filter(TransactionMaster.transaction_code == transaction_code).first()

    def create_transaction_master(self, transaction_data):
        """Create transaction master"""
        try:
            transaction = TransactionMaster(**transaction_data)
            self.session.add(transaction)
            self.session.commit()
            return transaction
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.TRANSACTION_CODE_ALREADY_EXISTS)

    def update_transaction_master(self, transaction_id, update_data):
        """Update transaction master"""
        transaction = self.get_transaction_master_by_id(transaction_id)
        if not transaction:
            raise CatalogingServiceException(error_codes.TRANSACTION_MASTER_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(transaction, key, value)
            self.session.commit()
            return transaction
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.TRANSACTION_CODE_ALREADY_EXISTS)

    def generate_transaction_code(self, entity_type, entity_id):
        """Generate unique transaction code"""
        # Simple implementation - can be enhanced with more sophisticated logic
        prefix_map = {
            'SKU': 'SKU',
            'PaymentMethod': 'PAY',
            'Department': 'DEPT',
            'Property': 'PROP'
        }
        prefix = prefix_map.get(entity_type, 'TXN')

        # Find the next available number
        existing_codes = self.session.query(TransactionMaster.transaction_code).filter(
            TransactionMaster.transaction_code.like(f'{prefix}%')
        ).all()

        max_num = 0
        for code_tuple in existing_codes:
            code = code_tuple[0]
            try:
                num = int(code.replace(prefix, ''))
                max_num = max(max_num, num)
            except ValueError:
                continue

        return f"{prefix}{max_num + 1:06d}"
