from sqlalchemy.orm import joinedload
from sqlalchemy.exc import IntegrityError
from cataloging_service.models import PaymentMethod, PropertyPaymentMethod
from cataloging_service.infrastructure.repositories.repository import BaseRepository
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class PaymentMethodRepository(BaseRepository):
    """Repository for payment method operations"""

    # Tenant-level Payment Method operations
    def get_all_payment_methods(self, include_inactive=False):
        """Get all tenant-level payment methods"""
        query = self.session.query(PaymentMethod)
        if not include_inactive:
            query = query.filter(PaymentMethod.is_active == True)
        return query.order_by(PaymentMethod.name).all()

    def get_payment_method_by_id(self, payment_method_id):
        """Get payment method by ID"""
        return self.session.query(PaymentMethod).filter(PaymentMethod.id == payment_method_id).first()

    def get_payment_method_by_code(self, code):
        """Get payment method by code"""
        return self.session.query(PaymentMethod).filter(PaymentMethod.code == code).first()

    def create_payment_method(self, payment_method_data):
        """Create payment method"""
        try:
            payment_method = PaymentMethod(**payment_method_data)
            self.session.add(payment_method)
            self.session.commit()
            return payment_method
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PAYMENT_METHOD_CODE_EXISTS)

    def update_payment_method(self, payment_method_id, update_data):
        """Update payment method"""
        payment_method = self.get_payment_method_by_id(payment_method_id)
        if not payment_method:
            raise CatalogingServiceException(error_codes.PAYMENT_METHOD_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(payment_method, key, value)
            self.session.commit()
            return payment_method
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PAYMENT_METHOD_CODE_EXISTS)

    # Property Payment Method operations
    def get_property_payment_methods(self, property_id, include_inactive=False):
        """Get all payment methods for a property"""
        query = self.session.query(PropertyPaymentMethod).options(
            joinedload(PropertyPaymentMethod.payment_method)
        ).filter(PropertyPaymentMethod.property_id == property_id)

        if not include_inactive:
            query = query.filter(PropertyPaymentMethod.is_active == True)

        return query.order_by(PropertyPaymentMethod.name).all()

    def get_property_payment_method_by_id(self, property_payment_method_id):
        """Get property payment method by ID"""
        return self.session.query(PropertyPaymentMethod).options(
            joinedload(PropertyPaymentMethod.payment_method)
        ).filter(PropertyPaymentMethod.id == property_payment_method_id).first()

    def create_property_payment_method(self, property_payment_method_data):
        """Create property payment method"""
        try:
            property_payment_method = PropertyPaymentMethod(**property_payment_method_data)
            self.session.add(property_payment_method)
            self.session.commit()
            return property_payment_method
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PROPERTY_PAYMENT_METHOD_EXISTS)

    def update_property_payment_method(self, property_payment_method_id, update_data):
        """Update property payment method"""
        property_payment_method = self.get_property_payment_method_by_id(property_payment_method_id)
        if not property_payment_method:
            raise CatalogingServiceException(error_codes.PROPERTY_PAYMENT_METHOD_NOT_FOUND)

        try:
            for key, value in update_data.items():
                setattr(property_payment_method, key, value)
            self.session.commit()
            return property_payment_method
        except IntegrityError:
            self.session.rollback()
            raise CatalogingServiceException(error_codes.PROPERTY_PAYMENT_METHOD_EXISTS)

    def inherit_payment_methods_from_tenant(self, property_id):
        """Inherit payment methods from tenant-level definitions"""
        # Get all auto-create payment methods
        payment_methods = self.session.query(PaymentMethod).filter(
            PaymentMethod.auto_create_on_property_launch == True,
            PaymentMethod.is_active == True
        ).all()

        created_methods = []
        for payment_method in payment_methods:
            try:
                property_payment_method = PropertyPaymentMethod(
                    property_id=property_id,
                    payment_method_id=payment_method.id,
                    name=payment_method.name,
                    code=payment_method.code,
                    is_custom=False,
                    config=payment_method.config,
                    is_active=True
                )
                self.session.add(property_payment_method)
                created_methods.append(property_payment_method)
            except IntegrityError:
                # Payment method already exists for this property
                continue

        self.session.commit()
        return created_methods
