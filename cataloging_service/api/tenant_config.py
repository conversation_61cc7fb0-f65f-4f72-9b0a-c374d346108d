from flask import Blueprint, jsonify
from flask import request

from cataloging_service.api.response_schema import TenantConfigResponseSchema
from cataloging_service.api.validators import TenantConfigRequestValidator
from cataloging_service.domain.tenant_config_service import TenantConfigService
from cataloging_service.infrastructure.decorators import raw_json

tenant_config_bp = Blueprint('TenantConfigs', __name__, url_prefix='/v1')


@tenant_config_bp.route('/tenant-configs', methods=['GET'])
def get_tenant_configs():
    """Fetch all configs
    ---
    operationId: get_tenant_configs
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get list of tenant configs for the current tenant
        tags:
            - Tenants
        responses:
            200:
                description: List of tenant configs
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/definitions/TenantConfigResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    property_id = request.args.get('property_id', None)
    seller_id = request.args.get('seller_id', None)
    tenant_configs = TenantConfigService().get_tenant_configs(property_id, seller_id)
    response = TenantConfigResponseSchema(many=True).dump(tenant_configs)
    return jsonify(response.data), 200


@tenant_config_bp.route('/tenant-configs', methods=['POST'])
@raw_json(TenantConfigRequestValidator)
def add_or_update_tenant_config(parsed_request):
    """Add or Update Tenant Config
    ---
    operationId: add_or_update_tenant_config
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add the tenant config if it is not exist or update the tenant config
        tags:
            - Tenants
        parameters:
            - in: body
              property_id: property for which config needs to add
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/TenantConfigRequestValidator"
        responses:
            201:
                description: Add the tenant config if it is not exist or update the tenant config
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            items:
                                $ref: "#/components/schemas/TenantConfigResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    property_id = request.args.get('property_id', None)
    tenant_config = TenantConfigService().create_or_update_tenant_config(parsed_request, property_id)
    response = TenantConfigResponseSchema().dump(tenant_config)
    return jsonify(response.data), 201



# @tenant_config_bp.route('/tenant-configs', methods=['POST'])
# @raw_json(TenantConfigRequestValidator)
# def create_tenant_config(parsed_request):
#     """create_tenant_config
#     ---
#     operationId: add_or_update_tenant_config
#     consumes:
#         - application/json
#     produces:
#         - application/json
#     schemes: ['http', 'https']
#     deprecated: false
#     post:
#         description: create_tenant_config
#         tags:
#             - Tenants
#         parameters:
#             - in: body
#               property_id: property for which config needs to add
#               required: True
#               schema:
#                 type: object
#                 properties:
#                     data:
#                         $ref: "#/components/schemas/TenantConfigRequestValidator"
#         responses:
#             201:
#                 description: create tenant config
#                 schema:
#                     type: object
#                     properties:
#                         data:
#                             type: object
#                             items:
#                                 $ref: "#/components/schemas/TenantConfigResponseSchema"
#                         meta:
#                             type: object
#                             additionalProperties: {}
#                         errors:
#                             type: array
#                             items:
#                                 $ref: "#/components/schemas/ApiErrorSchema"
#     """
#     property_id = request.args.get('property_id', None)
#     tenant_config = TenantConfigService().create_tenant_config(parsed_request, property_id)
#     response = TenantConfigResponseSchema().dump(tenant_config)
#     return jsonify(response.data), 201
#
# @tenant_config_bp.route('/tenant-configs', methods=['PATCH'])
# @raw_json(TenantConfigRequestValidator)
# def update_tenant_config(parsed_request):
#     """Add or Update Tenant Config
#     ---
#     operationId: add_or_update_tenant_config
#     consumes:
#         - application/json
#     produces:
#         - application/json
#     schemes: ['http', 'https']
#     deprecated: false
#     post:
#         description: Add the tenant config if it is not exist or update the tenant config
#         tags:
#             - Tenants
#         parameters:
#             - in: body
#               property_id: property for which config needs to add
#               required: True
#               schema:
#                 type: object
#                 properties:
#                     data:
#                         $ref: "#/components/schemas/TenantConfigRequestValidator"
#         responses:
#             201:
#                 description: Add the tenant config if it is not exist or update the tenant config
#                 schema:
#                     type: object
#                     properties:
#                         data:
#                             type: object
#                             items:
#                                 $ref: "#/components/schemas/TenantConfigResponseSchema"
#                         meta:
#                             type: object
#                             additionalProperties: {}
#                         errors:
#                             type: array
#                             items:
#                                 $ref: "#/components/schemas/ApiErrorSchema"
#     """
#     property_id = request.args.get('property_id', None)
#     tenant_config = TenantConfigService().update_tenant_config(parsed_request, property_id)
#     response = TenantConfigResponseSchema().dump(tenant_config)
#     return jsonify(response.data), 200
