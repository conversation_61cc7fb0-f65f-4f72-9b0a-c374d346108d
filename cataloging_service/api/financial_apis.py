from flask import Blueprint, request, jsonify
from marshmallow import Schema, fields, ValidationError
from cataloging_service.domain.financial_service import FinancialService
from cataloging_service.api.schemas import TransactionMasterSchema, TransactionDefaultMappingSchema
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.utils.decorators import raw_json
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('financial', __name__, url_prefix='/api/v1/financial')
financial_service = FinancialService()


class TransactionMasterCreateSchema(Schema):
    name = fields.String(required=True)
    entity_type = fields.String(required=True)
    transaction_type = fields.String(required=True)
    operational_unit_id = fields.String()
    operational_unit_type = fields.String()
    gl_code = fields.String()
    erp_id = fields.String()
    is_merge = fields.Boolean(missing=False)
    particulars = fields.String()
    hotel_entity_id = fields.String()
    franchiser_entity_id = fields.String()
    usali_code = fields.String()
    usali_category = fields.String()


class TransactionDefaultMappingCreateSchema(Schema):
    triggering_entity_type = fields.String(required=True)
    triggering_entity_category = fields.String(required=True)
    entity_type = fields.String(required=True)
    default_gl_code = fields.String()
    default_erp_id = fields.String()
    default_particulars = fields.String()
    default_is_merge = fields.Boolean(missing=True)


# Transaction Master APIs
@bp.route('/transaction-masters', methods=['GET'])
@raw_json
def get_transaction_masters():
    """Get all transaction masters"""
    try:
        entity_type = request.args.get('entity_type')
        status = request.args.get('status')
        transactions = financial_service.get_all_transaction_masters(entity_type, status)
        return jsonify({
            'transaction_masters': TransactionMasterSchema(many=True).dump(transactions)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting transaction masters")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/transaction-masters', methods=['POST'])
@raw_json
def create_transaction_master():
    """Create transaction master"""
    try:
        schema = TransactionMasterCreateSchema()
        data = schema.load(request.get_json())
        
        transaction = financial_service.create_transaction_master(data)
        return jsonify({
            'message': 'Transaction master created successfully',
            'transaction': TransactionMasterSchema().dump(transaction)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating transaction master")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/transaction-masters/<int:transaction_id>', methods=['GET'])
@raw_json
def get_transaction_master(transaction_id):
    """Get transaction master by ID"""
    try:
        transaction = financial_service.get_transaction_master(transaction_id)
        return jsonify({
            'transaction': TransactionMasterSchema().dump(transaction)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting transaction master")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/transaction-masters/<int:transaction_id>', methods=['PUT'])
@raw_json
def update_transaction_master(transaction_id):
    """Update transaction master"""
    try:
        schema = TransactionMasterCreateSchema()
        data = schema.load(request.get_json(), partial=True)
        
        transaction = financial_service.update_transaction_master(transaction_id, data)
        return jsonify({
            'message': 'Transaction master updated successfully',
            'transaction': TransactionMasterSchema().dump(transaction)
        }), 200
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error updating transaction master")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/transaction-masters/auto-generate', methods=['POST'])
@raw_json
def auto_generate_transaction_codes():
    """Auto-generate transaction codes for entity"""
    try:
        data = request.get_json()
        entity_type = data.get('entity_type')
        entity_id = data.get('entity_id')
        property_id = data.get('property_id')
        manage_franchiser_finance = data.get('manage_franchiser_finance', False)
        
        if not entity_type or not entity_id:
            return jsonify({'error': 'entity_type and entity_id are required'}), 400
        
        transactions = financial_service.auto_generate_transaction_codes_for_entity(
            entity_type, entity_id, property_id, manage_franchiser_finance
        )
        
        return jsonify({
            'message': 'Transaction codes generated successfully',
            'transactions': TransactionMasterSchema(many=True).dump(transactions)
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error auto-generating transaction codes")
        return jsonify({'error': 'Internal server error'}), 500


# Transaction Default Mapping APIs
@bp.route('/default-mappings', methods=['GET'])
@raw_json
def get_transaction_default_mappings():
    """Get transaction default mappings"""
    try:
        entity_type = request.args.get('entity_type')
        mappings = financial_service.get_transaction_default_mappings(entity_type)
        return jsonify({
            'default_mappings': TransactionDefaultMappingSchema(many=True).dump(mappings)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting transaction default mappings")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/default-mappings', methods=['POST'])
@raw_json
def create_transaction_default_mapping():
    """Create transaction default mapping"""
    try:
        schema = TransactionDefaultMappingCreateSchema()
        data = schema.load(request.get_json())
        
        mapping = financial_service.create_transaction_default_mapping(data)
        return jsonify({
            'message': 'Transaction default mapping created successfully',
            'mapping': TransactionDefaultMappingSchema().dump(mapping)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating transaction default mapping")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/generate-code', methods=['POST'])
@raw_json
def generate_transaction_code():
    """Generate transaction code with default mappings"""
    try:
        data = request.get_json()
        entity_type = data.get('entity_type')
        entity_id = data.get('entity_id')
        entity_category = data.get('entity_category')
        
        if not entity_type or not entity_id:
            return jsonify({'error': 'entity_type and entity_id are required'}), 400
        
        transaction_data = financial_service.generate_transaction_code_with_defaults(
            entity_type, entity_id, entity_category
        )
        
        return jsonify({
            'message': 'Transaction code generated successfully',
            'transaction_data': transaction_data
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error generating transaction code")
        return jsonify({'error': 'Internal server error'}), 500
