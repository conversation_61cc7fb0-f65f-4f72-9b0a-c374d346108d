from flask import Blueprint, request, jsonify
from marshmallow import Schema, fields, ValidationError
from cataloging_service.domain import service_provider
from cataloging_service.api.schemas import (
    DepartmentSchema, BrandDepartmentMappingSchema, PropertyDepartmentSchema,
    SkuDepartmentMappingSchema, PropertySkuDepartmentSchema, SellerTemplateSchema,
    SkuTerminalTypeAssignmentSchema, TransactionMasterSchema
)
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import raw_json
import logging

bp = Blueprint('department_apis', __name__)
logger = logging.getLogger(__name__)

department_service = service_provider.department_service


# Request Schemas for validation
class DepartmentCreateSchema(Schema):
    name = fields.String(required=True)
    code = fields.String(required=True)
    description = fields.String(allow_none=True)
    parent_id = fields.Integer(allow_none=True)
    financial_code = fields.String(allow_none=True)
    auto_create_on_property_launch = fields.Boolean(missing=False)


class DepartmentUpdateSchema(Schema):
    name = fields.String()
    description = fields.String(allow_none=True)
    parent_id = fields.Integer(allow_none=True)
    financial_code = fields.String(allow_none=True)
    auto_create_on_property_launch = fields.Boolean()


class PropertyDepartmentCreateSchema(Schema):
    name = fields.String(required=True)
    code = fields.String(required=True)
    description = fields.String(allow_none=True)
    tenant_department_id = fields.Integer(allow_none=True)
    parent_id = fields.Integer(allow_none=True)
    financial_code = fields.String(allow_none=True)
    is_custom = fields.Boolean(missing=True)


class SellerTemplateCreateSchema(Schema):
    name = fields.String(required=True)
    description = fields.String(allow_none=True)
    default_department_id = fields.Integer(allow_none=True)
    template_config = fields.Dict(allow_none=True)


# Department APIs
@bp.route('/departments', methods=['GET'])
@raw_json
def get_departments():
    """Get all tenant-level departments"""
    try:
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        departments = department_service.get_all_departments(include_inactive)
        return jsonify({
            'departments': DepartmentSchema(many=True).dump(departments)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting departments")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/departments', methods=['POST'])
@raw_json
def create_department():
    """Create new tenant-level department"""
    try:
        schema = DepartmentCreateSchema()
        data = schema.load(request.get_json())

        department = department_service.create_department(data)
        return jsonify({
            'message': 'Department created successfully',
            'department': DepartmentSchema().dump(department)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating department")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/departments/<int:department_id>', methods=['GET'])
@raw_json
def get_department(department_id):
    """Get department by ID"""
    try:
        department = department_service.get_department(department_id)
        return jsonify({
            'department': DepartmentSchema().dump(department)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting department")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/departments/<int:department_id>', methods=['PUT'])
@raw_json
def update_department(department_id):
    """Update department"""
    try:
        schema = DepartmentUpdateSchema()
        data = schema.load(request.get_json())

        department = department_service.update_department(department_id, data)
        return jsonify({
            'message': 'Department updated successfully',
            'department': DepartmentSchema().dump(department)
        }), 200
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error updating department")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/departments/<int:department_id>', methods=['DELETE'])
@raw_json
def delete_department(department_id):
    """Delete department (soft delete)"""
    try:
        department_service.delete_department(department_id)
        return jsonify({'message': 'Department deleted successfully'}), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error deleting department")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/departments/hierarchy', methods=['GET'])
@raw_json
def get_departments_hierarchy():
    """Get departments in hierarchical structure"""
    try:
        parent_id = request.args.get('parent_id', type=int)
        departments = department_service.get_departments_hierarchy(parent_id)
        return jsonify({
            'departments': DepartmentSchema(many=True).dump(departments)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting departments hierarchy")
        return jsonify({'error': 'Internal server error'}), 500


# Brand Department Mapping APIs
@bp.route('/brands/<int:brand_id>/departments', methods=['GET'])
@raw_json
def get_brand_departments(brand_id):
    """Get all departments mapped to a brand"""
    try:
        mappings = department_service.get_brand_departments(brand_id)
        return jsonify({
            'brand_departments': BrandDepartmentMappingSchema(many=True).dump(mappings)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting brand departments")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/brands/<int:brand_id>/departments/<int:department_id>', methods=['POST'])
@raw_json
def add_brand_department_mapping(brand_id, department_id):
    """Add department to brand"""
    try:
        mapping = department_service.add_brand_department_mapping(brand_id, department_id)
        return jsonify({
            'message': 'Department added to brand successfully',
            'mapping': BrandDepartmentMappingSchema().dump(mapping)
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error adding brand department mapping")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/brands/<int:brand_id>/departments/<int:department_id>', methods=['DELETE'])
@raw_json
def remove_brand_department_mapping(brand_id, department_id):
    """Remove department from brand"""
    try:
        department_service.remove_brand_department_mapping(brand_id, department_id)
        return jsonify({'message': 'Department removed from brand successfully'}), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error removing brand department mapping")
        return jsonify({'error': 'Internal server error'}), 500


# Property Department APIs
@bp.route('/properties/<string:property_id>/departments', methods=['GET'])
@raw_json
def get_property_departments(property_id):
    """Get all departments for a property"""
    try:
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        departments = department_service.get_property_departments(property_id, include_inactive)
        return jsonify({
            'property_departments': PropertyDepartmentSchema(many=True).dump(departments)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting property departments")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/properties/<string:property_id>/departments', methods=['POST'])
@raw_json
def create_property_department(property_id):
    """Create property department"""
    try:
        schema = PropertyDepartmentCreateSchema()
        data = schema.load(request.get_json())

        department = department_service.create_property_department(property_id, data)
        return jsonify({
            'message': 'Property department created successfully',
            'department': PropertyDepartmentSchema().dump(department)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating property department")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/properties/<string:property_id>/departments/<int:department_id>', methods=['GET'])
@raw_json
def get_property_department(property_id, department_id):
    """Get property department by ID"""
    try:
        department = department_service.get_property_department(department_id)
        return jsonify({
            'department': PropertyDepartmentSchema().dump(department)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting property department")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/properties/<string:property_id>/departments/<int:department_id>', methods=['PUT'])
@raw_json
def update_property_department(property_id, department_id):
    """Update property department"""
    try:
        schema = PropertyDepartmentCreateSchema()  # Reuse create schema for updates
        data = schema.load(request.get_json(), partial=True)

        department = department_service.update_property_department(department_id, data)
        return jsonify({
            'message': 'Property department updated successfully',
            'department': PropertyDepartmentSchema().dump(department)
        }), 200
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error updating property department")
        return jsonify({'error': 'Internal server error'}), 500


# SKU Department Mapping APIs
@bp.route('/skus/<string:sku_id>/departments', methods=['GET'])
@raw_json
def get_sku_departments(sku_id):
    """Get departments mapped to a SKU"""
    try:
        mappings = department_service.get_sku_departments(sku_id)
        return jsonify({
            'sku_departments': SkuDepartmentMappingSchema(many=True).dump(mappings)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting SKU departments")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/skus/<string:sku_id>/departments/<int:department_id>', methods=['POST'])
@raw_json
def add_sku_department_mapping(sku_id, department_id):
    """Add SKU to department"""
    try:
        mapping = department_service.add_sku_department_mapping(sku_id, department_id)
        return jsonify({
            'message': 'SKU added to department successfully',
            'mapping': SkuDepartmentMappingSchema().dump(mapping)
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error adding SKU department mapping")
        return jsonify({'error': 'Internal server error'}), 500


# Seller Template APIs
@bp.route('/seller-templates', methods=['GET'])
@raw_json
def get_seller_templates():
    """Get all seller templates"""
    try:
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        templates = department_service.get_all_seller_templates(include_inactive)
        return jsonify({
            'seller_templates': SellerTemplateSchema(many=True).dump(templates)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting seller templates")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/seller-templates', methods=['POST'])
@raw_json
def create_seller_template():
    """Create seller template"""
    try:
        schema = SellerTemplateCreateSchema()
        data = schema.load(request.get_json())

        template = department_service.create_seller_template(data)
        return jsonify({
            'message': 'Seller template created successfully',
            'template': SellerTemplateSchema().dump(template)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating seller template")
        return jsonify({'error': 'Internal server error'}), 500


# SKU Terminal Type Assignment APIs
@bp.route('/skus/<string:sku_id>/terminal-types', methods=['GET'])
@raw_json
def get_sku_terminal_type_assignments(sku_id):
    """Get terminal type assignments for a SKU"""
    try:
        assignments = department_service.get_sku_terminal_type_assignments(sku_id)
        return jsonify({
            'sku_terminal_types': SkuTerminalTypeAssignmentSchema(many=True).dump(assignments)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting SKU terminal type assignments")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/skus/<string:sku_id>/terminal-types/<int:terminal_type_id>', methods=['POST'])
@raw_json
def add_sku_terminal_type_assignment(sku_id, terminal_type_id):
    """Add SKU to terminal type"""
    try:
        assignment = department_service.add_sku_terminal_type_assignment(sku_id, terminal_type_id)
        return jsonify({
            'message': 'SKU added to terminal type successfully',
            'assignment': SkuTerminalTypeAssignmentSchema().dump(assignment)
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error adding SKU terminal type assignment")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/skus/<string:sku_id>/terminal-types/<int:terminal_type_id>', methods=['DELETE'])
@raw_json
def remove_sku_terminal_type_assignment(sku_id, terminal_type_id):
    """Remove SKU from terminal type"""
    try:
        department_service.remove_sku_terminal_type_assignment(sku_id, terminal_type_id)
        return jsonify({'message': 'SKU removed from terminal type successfully'}), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error removing SKU terminal type assignment")
        return jsonify({'error': 'Internal server error'}), 500


# Transaction Master APIs
class TransactionMasterCreateSchema(Schema):
    name = fields.String(required=True)
    entity_type = fields.String(required=True)
    transaction_type = fields.String(required=True)
    operational_unit_id = fields.String()
    operational_unit_type = fields.String()
    gl_code = fields.String()
    erp_id = fields.String()
    is_merge = fields.Boolean(missing=False)
    particulars = fields.String()
    hotel_entity_id = fields.String()
    franchiser_entity_id = fields.String()
    usali_code = fields.String()
    usali_category = fields.String()


@bp.route('/transaction-masters', methods=['GET'])
@raw_json
def get_transaction_masters():
    """Get all transaction masters"""
    try:
        entity_type = request.args.get('entity_type')
        status = request.args.get('status')
        transactions = department_service.get_all_transaction_masters(entity_type, status)
        return jsonify({
            'transaction_masters': TransactionMasterSchema(many=True).dump(transactions)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting transaction masters")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/transaction-masters', methods=['POST'])
@raw_json
def create_transaction_master():
    """Create transaction master"""
    try:
        schema = TransactionMasterCreateSchema()
        data = schema.load(request.get_json())

        transaction = department_service.create_transaction_master(data)
        return jsonify({
            'message': 'Transaction master created successfully',
            'transaction': TransactionMasterSchema().dump(transaction)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating transaction master")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/transaction-masters/<int:transaction_id>', methods=['GET'])
@raw_json
def get_transaction_master(transaction_id):
    """Get transaction master by ID"""
    try:
        transaction = department_service.get_transaction_master(transaction_id)
        return jsonify({
            'transaction': TransactionMasterSchema().dump(transaction)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting transaction master")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/transaction-masters/<int:transaction_id>', methods=['PUT'])
@raw_json
def update_transaction_master(transaction_id):
    """Update transaction master"""
    try:
        schema = TransactionMasterCreateSchema()
        data = schema.load(request.get_json(), partial=True)

        transaction = department_service.update_transaction_master(transaction_id, data)
        return jsonify({
            'message': 'Transaction master updated successfully',
            'transaction': TransactionMasterSchema().dump(transaction)
        }), 200
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error updating transaction master")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/transaction-masters/auto-generate', methods=['POST'])
@raw_json
def auto_generate_transaction_codes():
    """Auto-generate transaction codes for entity"""
    try:
        data = request.get_json()
        entity_type = data.get('entity_type')
        entity_id = data.get('entity_id')
        property_id = data.get('property_id')
        manage_franchiser_finance = data.get('manage_franchiser_finance', False)

        if not entity_type or not entity_id:
            return jsonify({'error': 'entity_type and entity_id are required'}), 400

        transactions = department_service.auto_generate_transaction_codes_for_entity(
            entity_type, entity_id, property_id, manage_franchiser_finance
        )

        return jsonify({
            'message': 'Transaction codes generated successfully',
            'transactions': TransactionMasterSchema(many=True).dump(transactions)
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error auto-generating transaction codes")
        return jsonify({'error': 'Internal server error'}), 500


# Business Logic Validation APIs
@bp.route('/validate/sku-ownership', methods=['POST'])
@raw_json
def validate_sku_ownership():
    """Validate SKU ownership rules"""
    try:
        data = request.get_json()
        property_id = data.get('property_id')
        sku_id = data.get('sku_id')
        seller_id = data.get('seller_id')

        if not all([property_id, sku_id, seller_id]):
            return jsonify({'error': 'property_id, sku_id, and seller_id are required'}), 400

        is_valid = department_service.validate_sku_ownership(property_id, sku_id, seller_id)
        return jsonify({
            'valid': is_valid,
            'message': 'SKU ownership validation passed'
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message, 'valid': False}), e.status_code
    except Exception as e:
        logger.exception("Error validating SKU ownership")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/validate/terminal-type-seller', methods=['POST'])
@raw_json
def validate_terminal_type_seller():
    """Validate terminal type-seller relation"""
    try:
        data = request.get_json()
        property_id = data.get('property_id')
        seller_id = data.get('seller_id')
        terminal_type_id = data.get('terminal_type_id')

        if not all([property_id, seller_id, terminal_type_id]):
            return jsonify({'error': 'property_id, seller_id, and terminal_type_id are required'}), 400

        is_valid = department_service.validate_terminal_type_seller_relation(property_id, seller_id, terminal_type_id)
        return jsonify({
            'valid': is_valid,
            'message': 'Terminal type-seller validation passed'
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message, 'valid': False}), e.status_code
    except Exception as e:
        logger.exception("Error validating terminal type-seller relation")
        return jsonify({'error': 'Internal server error'}), 500
