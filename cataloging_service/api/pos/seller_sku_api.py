import logging

from flask import jsonify, Blueprint, request

from cataloging_service.api.response_schema import MenuCategoryResponseSchema, SellerSkuResponseSchema
from cataloging_service.api.schemas import SellerSchema
from cataloging_service.domain import service_provider

logger = logging.getLogger(__name__)

seller_service = service_provider.seller_service

bp = Blueprint('pos_skus', __name__, url_prefix='/cataloging-service/api/v1')


@bp.route('/sellers/<string:seller_id>/skus', methods=['GET'])
def get_seller_skus(seller_id):
    """
    Get seller skus, for the given seller_id
    ---
    operationId: get_seller_skus
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - POS
        parameters:
            - name: seller_id
              in: path
              required: True
              schema:
                type: string
        description: Get seller skus for given seller_id
        responses:
            '200':
                description: List of SKUs
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                skus:
                                    type: array
                                    items:
                                        $ref: '#/components/schemas/SellerSkuResponseSchema'
    """
    seller_skus = seller_service.get_seller_skus(seller_id=seller_id)
    response = SellerSkuResponseSchema(many=True).dump(seller_skus).data
    return jsonify({"skus": response}), 200


@bp.route('/menu-categories', methods=['GET'])
def get_menu_categories():
    """
    Get all menu-categories as per the filter criteria
    ---
    operationId: get_menu_categories
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - POS
        description: Get all menu categories as per the filter criteria. If there is no filter criteria, then get all
            menu categories
        parameters:
            - name: seller_category_id
              in: query
              required: True
              schema:
                  type: integer
        responses:
            '200':
                description: List of MenuCategories
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                menu_categories:
                                    type: array
                                    items:
                                        $ref: '#/components/schemas/MenuCategoryResponseSchema'
    """
    menu_categories = seller_service.get_menu_categories(seller_category_id=request.args.get('seller_category_id'))
    response = MenuCategoryResponseSchema(many=True).dump(menu_categories).data
    return jsonify({"menu_categories": response}), 200
