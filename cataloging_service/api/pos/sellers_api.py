import logging

from flask import jsonify, request, Blueprint

from cataloging_service.api.response_schema import SellerResponseSchema
from cataloging_service.domain import service_provider

logger = logging.getLogger(__name__)

seller_service = service_provider.seller_service

bp = Blueprint('pos_sellers', __name__, url_prefix='/cataloging-service/api/v1')


@bp.route('/sellers', methods=['GET'])
def get_sellers():
    """
    Get sellers, with provided filter criteria
    ---
    operationId: get_sellers
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - POS
        parameters:
            - name: property_id
              in: query
              required: False
              schema:
                type: string
        description: Get sellers with provided filter
        responses:
            '200':
                description: List of Sellers as per the filter criteria
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                sellers:
                                    type: array
                                    items:
                                        $ref: '#/components/schemas/SellerResponseSchema'
    """
    sellers = seller_service.get_sellers(property_id=request.args.get("property_id"),
                                         seller_category_id=request.args.get("seller_category_id"))
    response = SellerResponseSchema(many=True).dump(sellers).data
    return jsonify({"sellers": response}), 200
