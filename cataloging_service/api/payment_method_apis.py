from flask import Blueprint, request, jsonify
from marshmallow import Schema, fields, ValidationError
from cataloging_service.domain.payment_method_service import PaymentMethodService
from cataloging_service.api.schemas import PaymentMethodSchema, PropertyPaymentMethodSchema
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.utils.decorators import raw_json
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('payment_methods', __name__, url_prefix='/api/v1/payment-methods')
payment_method_service = PaymentMethodService()


class PaymentMethodCreateSchema(Schema):
    name = fields.String(required=True)
    code = fields.String(required=True)
    payment_method_type = fields.String()
    paid_to = fields.String()
    allowed_paid_by = fields.String()
    auto_create_on_property_launch = fields.Boolean(missing=False)
    config = fields.Dict()


class PropertyPaymentMethodCreateSchema(Schema):
    property_id = fields.String(required=True)
    payment_method_id = fields.Integer()
    name = fields.String(required=True)
    code = fields.String(required=True)
    config = fields.Dict()


# Tenant-level Payment Method APIs
@bp.route('/', methods=['GET'])
@raw_json
def get_payment_methods():
    """Get all tenant-level payment methods"""
    try:
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        payment_methods = payment_method_service.get_all_payment_methods(include_inactive)
        return jsonify({
            'payment_methods': PaymentMethodSchema(many=True).dump(payment_methods)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting payment methods")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/', methods=['POST'])
@raw_json
def create_payment_method():
    """Create tenant-level payment method"""
    try:
        schema = PaymentMethodCreateSchema()
        data = schema.load(request.get_json())
        
        # Validate configuration
        payment_method_service.validate_payment_method_configuration(data)
        
        payment_method = payment_method_service.create_payment_method(data)
        return jsonify({
            'message': 'Payment method created successfully',
            'payment_method': PaymentMethodSchema().dump(payment_method)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating payment method")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/<int:payment_method_id>', methods=['GET'])
@raw_json
def get_payment_method(payment_method_id):
    """Get payment method by ID"""
    try:
        payment_method = payment_method_service.get_payment_method(payment_method_id)
        return jsonify({
            'payment_method': PaymentMethodSchema().dump(payment_method)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting payment method")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/<int:payment_method_id>', methods=['PUT'])
@raw_json
def update_payment_method(payment_method_id):
    """Update payment method"""
    try:
        schema = PaymentMethodCreateSchema()
        data = schema.load(request.get_json(), partial=True)
        
        # Validate configuration if provided
        if data:
            payment_method_service.validate_payment_method_configuration(data)
        
        payment_method = payment_method_service.update_payment_method(payment_method_id, data)
        return jsonify({
            'message': 'Payment method updated successfully',
            'payment_method': PaymentMethodSchema().dump(payment_method)
        }), 200
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error updating payment method")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/<int:payment_method_id>/activate', methods=['POST'])
@raw_json
def activate_payment_method(payment_method_id):
    """Activate payment method"""
    try:
        payment_method = payment_method_service.activate_payment_method(payment_method_id)
        return jsonify({
            'message': 'Payment method activated successfully',
            'payment_method': PaymentMethodSchema().dump(payment_method)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error activating payment method")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/<int:payment_method_id>/deactivate', methods=['POST'])
@raw_json
def deactivate_payment_method(payment_method_id):
    """Deactivate payment method"""
    try:
        payment_method = payment_method_service.deactivate_payment_method(payment_method_id)
        return jsonify({
            'message': 'Payment method deactivated successfully',
            'payment_method': PaymentMethodSchema().dump(payment_method)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error deactivating payment method")
        return jsonify({'error': 'Internal server error'}), 500


# Property Payment Method APIs
@bp.route('/properties/<string:property_id>', methods=['GET'])
@raw_json
def get_property_payment_methods(property_id):
    """Get payment methods for a property"""
    try:
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        payment_methods = payment_method_service.get_property_payment_methods(property_id, include_inactive)
        return jsonify({
            'property_payment_methods': PropertyPaymentMethodSchema(many=True).dump(payment_methods)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting property payment methods")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/properties/<string:property_id>', methods=['POST'])
@raw_json
def create_property_payment_method(property_id):
    """Create property payment method"""
    try:
        schema = PropertyPaymentMethodCreateSchema()
        data = schema.load(request.get_json())
        data['property_id'] = property_id  # Ensure property_id matches URL
        
        property_payment_method = payment_method_service.create_property_payment_method(data)
        return jsonify({
            'message': 'Property payment method created successfully',
            'property_payment_method': PropertyPaymentMethodSchema().dump(property_payment_method)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating property payment method")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/properties/<string:property_id>/inherit', methods=['POST'])
@raw_json
def inherit_payment_methods_for_property(property_id):
    """Inherit payment methods from tenant-level for a property"""
    try:
        inherited_methods = payment_method_service.inherit_payment_methods_for_property(property_id)
        return jsonify({
            'message': 'Payment methods inherited successfully',
            'inherited_methods': PropertyPaymentMethodSchema(many=True).dump(inherited_methods),
            'count': len(inherited_methods)
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error inheriting payment methods")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/properties/<string:property_id>/setup', methods=['POST'])
@raw_json
def setup_property_payment_methods(property_id):
    """Complete setup of payment methods for a property"""
    try:
        data = request.get_json() or {}
        custom_methods = data.get('custom_methods', [])
        
        result = payment_method_service.setup_property_payment_methods(property_id, custom_methods)
        return jsonify({
            'message': 'Property payment methods setup completed',
            'result': {
                'inherited_count': len(result['inherited_methods']),
                'custom_count': len(result['custom_methods']),
                'total_count': result['total_count']
            },
            'inherited_methods': PropertyPaymentMethodSchema(many=True).dump(result['inherited_methods']),
            'custom_methods': PropertyPaymentMethodSchema(many=True).dump(result['custom_methods'])
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error setting up property payment methods")
        return jsonify({'error': 'Internal server error'}), 500
