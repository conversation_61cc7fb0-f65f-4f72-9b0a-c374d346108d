from marshmallow import Schema, fields

from cataloging_service.api.schemas import BrandSchema, GuestFacingProcessSchema, LocationSchema, \
    NeighbouringPlaceSchema, PropertyDetailSchema, PropertyImageSchema, SkuCategorySchema, GuestTypeSchema, \
    DescriptionSchema, PropertyVideoSchema
from cataloging_service.controllers.serializer import Policy
from cataloging_service.extensions import ma
from cataloging_service.models import RoomTypeConfiguration, RoomType


class OwnerSchemaWithPrimary(Schema):
    education = fields.String()
    email = fields.String()
    first_name = fields.String()
    gender = fields.String()
    id = fields.Integer()
    is_primary_owner = fields.Boolean()
    last_name = fields.String()
    middle_name = fields.String()
    occupation = fields.String()
    phone_number = fields.String()


class LandmarkSchema(Schema):
    hatchback_cab_fare = fields.Decimal()
    hotel_direction = fields.String()
    hotel_distance = fields.Decimal()
    id = fields.Integer()
    latitude = fields.Float()
    longitude = fields.Float()
    name = fields.String()
    sedan_cab_fare = fields.Decimal()
    type = fields.String()


class RoomTypeEntitySchema(ma.Schema):
    class Meta:
        model = RoomType
        fields = ('id', 'code', 'type', 'unirate_room_type_code', 'crs_room_type_code', 'bb_room_type_code',
                  'associated_modular_sku_code')


class RoomTypeConfigurationEntitySchema(ma.Schema):
    room_type = ma.Nested(RoomTypeEntitySchema)
    ext_provider_code = ma.Method('get_external_provider')

    def get_external_provider(self, room_type_configuration):
        return room_type_configuration.provider.code if room_type_configuration.ext_id else None

    class Meta:
        model = RoomTypeConfiguration
        fields = ('id', 'room_type', 'extra_bed', 'min_occupancy', 'max_occupancy', 'adults', 'mm_id', 'children',
                  'max_total', 'room_count', 'min_room_size', 'ext_provider_code', 'ext_room_code', 'ext_room_name',
                  'description', 'display_name', 'ext_rate_plan_code', 'ext_rate_plan_name')


class PropertySkuSchema(Schema):
    default_list_price = fields.Decimal()
    default_sale_price = fields.Decimal()
    hsn_sac = fields.String()
    id = fields.Integer()
    is_sku_saleable = fields.Boolean()
    property_id = fields.String()
    saleable = fields.Boolean()
    sku_category_code = fields.String()
    sku_code = fields.String()
    sku_name = fields.String()
    status = fields.String()


class PropertyTransportStation(Schema):
    hatchback_cab_fare = fields.Decimal()
    hotel_direction = fields.String()
    hotel_distance = fields.Decimal()
    latitude = fields.Float()
    longitude = fields.Float()
    name = fields.String()
    sedan_cab_fare = fields.Decimal()


class PropertyEntitySchema(Schema):
    amenity_summary = fields.Dict()
    brands = fields.Nested(BrandSchema, many=True)
    churned_date = fields.LocalDateTime()
    contractual_launch_date = fields.Date()
    description = fields.Nested(DescriptionSchema)
    guest_facing_details = fields.Nested(GuestFacingProcessSchema, attribute='guest_facing_process')
    hx_id = fields.String()
    external_hotel_id = fields.String()
    id = fields.String()
    landmarks = fields.Nested(LandmarkSchema, many=True, attribute='property_landmarks')
    launched_date = fields.Date()
    location = fields.Nested(LocationSchema)
    name = fields.Dict()
    neighbouring_places = fields.Nested(NeighbouringPlaceSchema, attribute='neighbouring_place')
    owners = fields.Nested(OwnerSchemaWithPrimary, many=True)
    policies = fields.Nested(Policy, many=True)
    property_details = fields.Nested(PropertyDetailSchema, attribute='property_detail')
    property_images = fields.Nested(PropertyImageSchema, many=True)
    property_videos = fields.Nested(PropertyVideoSchema, many=True)
    room_count = fields.Integer()
    room_type_configs = fields.Nested(RoomTypeConfigurationEntitySchema, many=True,
                                      attribute='room_type_configurations')
    signed_date = fields.Date()
    sku_categories = fields.Nested(SkuCategorySchema, many=True)
    skus = fields.Nested(PropertySkuSchema, many=True)
    status = fields.String()
    suited_to = fields.Nested(GuestTypeSchema, many=True)
    transport_stations = fields.Nested(PropertyTransportStation, many=True)
    logo = fields.String()
    timezone = fields.String()
    base_currency_code = fields.String()
    current_business_date = fields.Date()
    country_code = fields.String()
    is_test = fields.Boolean()
    cost_center_id = fields.String()
    region = fields.String()

