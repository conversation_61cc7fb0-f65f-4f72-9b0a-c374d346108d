from flask import Blueprint, jsonify
from flask import request

from cataloging_service.api.response_schema import UserDefinedEnumResponseSchema
from cataloging_service.domain.enum_config_service import EnumConfigService

user_defined_enum_bp = Blueprint('UserDefinedEnums', __name__, url_prefix='/v1')


@user_defined_enum_bp.route('/enums', methods=['GET'])
def get_enums():
    """Fetch all enums
    ---
    operationId: get_enums
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get list of enums for the current property
        tags:
            - Enums
        responses:
            200:
                description: List of user defined enums
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/definitions/UserDefinedEnumResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    property_id = request.args.get('property_id', None)
    enum_names = request.args.get('enum_names', None)
    seller_id = request.args.get('seller_id', None)
    user_type = request.headers.get("X-User-Type") or None
    if enum_names:
        enum_names = [enum_name.strip() for enum_name in enum_names.split(',')]
    user_defined_enums = EnumConfigService().get_enums(property_id, seller_id, enum_names, role=user_type)
    response = UserDefinedEnumResponseSchema(many=True).dump(user_defined_enums)
    return jsonify(response.data), 200
