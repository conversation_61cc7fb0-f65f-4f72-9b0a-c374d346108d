from flask import Blueprint, jsonify
from flask import request

from cataloging_service.api.response_schema import TenantConfigResponseSchema
from cataloging_service.domain.tenant_config_service import TenantConfigService

tenant_config_v2_bp = Blueprint('TenantConfigsV2', __name__, url_prefix='/v2')


@tenant_config_v2_bp.route('/tenant-configs', methods=['GET'])
def get_tenant_configs():
    """Fetch all configs
    ---
    operationId: get_tenant_configs_v2
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get list of tenant configs for the current tenant
        tags:
            - Tenants
        responses:
            200:
                description: List of tenant configs
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/definitions/TenantConfigResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    property_id = request.args.get('property_id', None)
    seller_id = request.args.get('seller_id', None)
    config_name = request.args.get('config_name', None)
    tenant_configs = TenantConfigService().get_tenant_configs_v2(property_id, seller_id, config_name)
    response = TenantConfigResponseSchema(many=True).dump(tenant_configs)
    return jsonify(response.data), 200
