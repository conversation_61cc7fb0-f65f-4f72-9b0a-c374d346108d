from flask import request
from marshmallow import ValidationError
from sqlalchemy.exc import DataError

from cataloging_service.api.v3.schemas.property import PropertySchema
from cataloging_service.constants.model_choices import PropertyChoices
from cataloging_service.domain import service_provider
from cataloging_service.utils import docstring_parameter
from core.common.api import BaseAPI
from core.common.api.api_response import response
from core.common.api.api_response_schema import PaginationSchema


class PropertySearchAPI(BaseAPI):

    @docstring_parameter(status_choices=','.join(PropertyChoices.STATUS_CHOICES))
    def get(self):
        # TODO: 1,678.79 calls
        """
        ---
        description: Property Search API
        tags:
            - Search Property
        parameters:
            - in: query
              name: name
              schema:
                type: string
              description: partial name of property
              required: False
            - in: query
              name: id
              schema:
                type: string
              description: exact match of the id of a property
              required: False
            - in: query
              name: status
              schema:
                type: string
                enum: [{status_choices}]
              description: exact status allowed for properties
              required: False
            - in: query
              name: fields
              schema:
                type: string
              description: "Comma separated fields of schema. Also can do nested fields.
                \n For example: location.address, legal_details.address.city.name"
              required: False
            - in: query
              name: page
              schema:
                type: integer
              description: page number if the response is paginated
              required: False
            - in: query
              name: per_page
              schema:
                type: integer
              description: records per page if the reponse is paginated
              required: False
        responses:
          200:
            description: OK
            content:
              application/json:
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/PropertySchema"
                        meta:
                            type: object
                            properties:
                                pagination:
                                    $ref: "#/components/schemas/PaginationSchema"
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/APIErrorSchema"
          400:
            description: Bad Request, Invalid page, Invalid Max allowed results per page
            content:
              application/json:
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                        meta:
                            type: object
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/APIErrorSchema"
          500:
            description: Unexpected exception
            content:
              application/json:
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                        meta:
                            type: object
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/APIErrorSchema"
        """
        name_filter = request.args.get('name', '')
        id_filter = request.args.get('id', '')
        city_id_filter = request.args.get('city_id', '')
        status_filter = request.args.get('status', '')

        fields = request.args.get('fields')
        fields = fields.split(',') if fields else None

        query_filter = request.args.get('query')
        if query_filter and (id_filter or name_filter or city_id_filter):
            raise ValidationError("Query filter cannot be used with id, city_id or name filter")

        if query_filter and query_filter.isdigit():
            id_filter = query_filter
        elif query_filter:
            name_filter = query_filter

        paginate = True
        properties = service_provider.property_service.search_properties(
            id=id_filter,
            city_id=city_id_filter,
            statuses=status_filter,
            fuzzy_name=name_filter,
            paginate=paginate,
        )

        properties_dump = PropertySchema(many=True, only=fields or None).dump(properties.items)
        response.data = properties_dump.data
        if paginate:
            pagination_data = PaginationSchema().dump(properties).data
            response.meta = {
                'pagination': pagination_data
            }

        return response


@PropertySearchAPI.exception_handler.register([ValidationError, DataError])
def handle_validation_error(exc, context):
    response.add_error_from_exception(exc)
    response.status_code = 400
    return response


property_search_api_method_view = PropertySearchAPI.as_view('property_search')
