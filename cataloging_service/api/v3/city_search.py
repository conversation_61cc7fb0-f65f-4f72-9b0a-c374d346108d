from flask import request
from flask_login import login_required
from flask_marshmallow import Schema
from marshmallow import fields

from cataloging_service.infrastructure.repositories.pagination import Pagination
from core.common.api import BaseAPI
from core.common.api.api_response import response
from cataloging_service.domain import service_provider

location_service = service_provider.location_service


class CitySchema(Schema):
    id = fields.String()
    name = fields.String()
    state = fields.String()


class CitySearch(BaseAPI):

    @login_required
    def get(self):
        # TODO: 35.46 calls
        query = request.args.get('q')
        if query:
            filter = '%{query}%'.format(query=query)
            cities = location_service.get_all_related_cities(filter)
        else:
            cities = location_service.get_all_cities()

        cities = Pagination.paginate(cities, error_out=False)
        cities_dump = CitySchema(many=True).dump(cities.items)
        response.data = cities_dump.data
        return response
