import codecs
import csv

from flask import request, render_template

from cataloging_service.api.new_schemas.pos_menu_order_csv_schema import CsvUploader
from cataloging_service.api.new_schemas.hygiene_shield_csv_schema import HygieneShieldCsvUploader
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.domain import service_provider
from core.common.api import BaseAPI
from core.pos.upload_csv import POSMenuUpload
from core.property.hygiene_shield_upload_csv import HygieneShieldUpload

class UploadPosMenuAPI(BaseAPI):
    def get(self):
        return render_template("csv_upload.html")

    def post(self):
        flask_file = request.files['data']
        stream = codecs.iterdecode(flask_file.stream, 'utf-8')
        csv_file = csv.DictReader(stream)
        error_dict = {}
        pos_menus = []

        for row in csv_file:
            data = CsvUploader().load(data=row)
            if data.errors:
                error_dict[csv_file.line_num] = data.errors
            else:
                pos_menus.append(data.data)

        if error_dict:
            return render_template("csv_upload.html", errors=error_dict, code=400)

        error = POSMenuUpload().upload_csv(pos_menus)
        if error:
            return render_template("csv_upload.html", errors=error, code=400)
        else:
            return render_template("csv_upload.html", code=200)


class UploadHygieneShieldAPI(BaseAPI):
    def get(self):
        return render_template("hygiene_shield_csv_upload.html")

    def post(self):
        flask_file = request.files['data']
        stream = codecs.iterdecode(flask_file.stream, 'utf-8')
        csv_file = csv.DictReader(stream)
        error_dict = {}
        hygiene_shield_details = []
        empty_hygiene_shield_properties = []
        property_service = service_provider.property_service

        for row in csv_file:
            if not row['hygiene_shield_name']:
                property_id = row['property_id']
                empty_hygiene_shield_properties.append(property_id)
            else:
                data = HygieneShieldCsvUploader().load(data=row)
                if data.errors:
                    error_dict[csv_file.line_num] = data.errors
                else:
                    hygiene_shield_details.append(data.data)

        empty_hygiene_shield_properties = property_service.get_properties_in_bulk(empty_hygiene_shield_properties)
        for empty_hygiene_shield_property in empty_hygiene_shield_properties:
            empty_hygiene_shield_property.property_detail.hygiene_shield_name = None
        repo_provider.property_repository.persist_all(empty_hygiene_shield_properties)
        repo_provider.property_repository.session().commit()

        if error_dict:
            return render_template("hygiene_shield_csv_upload.html", errors=error_dict, code=400)

        error = HygieneShieldUpload().upload_csv(hygiene_shield_details)
        if error:
            return render_template("hygiene_shield_csv_upload.html", errors=error, code=400)
        else:
            return render_template("hygiene_shield_csv_upload.html", code=200)