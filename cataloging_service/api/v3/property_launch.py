from flask import render_template
from flask import request
from flask_login import login_required
from marshmallow import ValidationError

from cataloging_service.api.v3.schemas.property_launch import PropertyLaunchSchema
from core.common.api import BaseAPI
from core.common.api.api_response import response


class PropertyLaunchAPI(BaseAPI):

    @login_required
    def get(self):
        return render_template('property_launch_v2.html')

    @login_required
    def post(self):
        # TODO: 65.24 calls
        data = self._nest_data(request.json)
        serializer = PropertyLaunchSchema().load(data)
        if serializer.errors:
            raise ValidationError(message="Invalid data", errors=serializer.errors)
        from core.property.property_launch import launch_property
        new_property = launch_property(PropertyLaunchSchema().dump(serializer.data))
        redirect_url = 'https://docs.google.com/forms/d/e/1FAIpQLSfbhE5InO2x0WkTpMA6rVXuFst1A514p_jLC9jEfL74VWmvDw/' \
                       'viewform?usp=pp_url&entry.749682308={}'.format(new_property.id)
        response.data = {"property_id": new_property.id,
                         "redirect_url": redirect_url}
        return response

    def _nest_data(self, data):
        address_data = {k.replace('address_', ''): v for k, v in data.items() if k.startswith('address_')}

        legal_address_data = {k.replace('legal_address_', ''): v for k, v in data.items()
                              if k.startswith('legal_address_')}
        facilities = {k: v for k, v in data.items() if k.startswith('is_')}
        data['address'] = address_data
        same_as_contact_address = data.get('same_as_contact_address', False)
        if same_as_contact_address:
            data['legal_address'] = address_data.copy()
        else:
            data['legal_address'] = legal_address_data

        data['facilities'] = facilities
        data['room_type_details'] = [r for r in data['room_type_details'] if int(r['is_room_type_available'])]
        return data


@PropertyLaunchAPI.exception_handler.register([ValidationError, ValueError])
def handle_validation_error(exc, context):
    response.add_error_from_exception(exc)
    response.status_code = 400
    return response
