from flask import render_template
from flask import request
from flask_login import login_required
from marshmallow import ValidationError

from cataloging_service.api.v3.schemas.user_role_creation import UserRoleCreationSchema
from core.common.api import BaseAPI
from core.common.api.api_response import response


class UserRoleCreationAPI(BaseAPI):

    @login_required
    def get(self):
        return render_template('user_role_creation_form.html')

    @login_required
    def post(self):
        data = request.json
        serializer = UserRoleCreationSchema().load(data)
        if serializer.errors:
            raise ValidationError(message="Invalid data", errors=serializer.errors)
        from core.property.user_role_creation import user_role_creation
        user_role_creation(UserRoleCreationSchema().dump(serializer.data))
        return


@UserRoleCreationAPI.exception_handler.register([ValidationError, ValueError])
def handle_validation_error(exc, context):
    response.add_error_from_exception(exc)
    response.status_code = 400
    return response
