from flask import Blueprint, jsonify, request
from cataloging_service.infrastructure.decorators import raw_json, query_params
from cataloging_service.domain import service_provider
from cataloging_service.api.validators import (
    ItemCreateRequestValidator, ItemEditRequestValidator,
    VariantGroupEditRequestValidator, VariantGroupCreateRequestValidator, ItemSearchRequestValidator
)
from cataloging_service.api.request_objects import (
    ItemEditRequest, ItemCreateRequest, VariantGroupCreateRequest,
    VariantGroupEditRequest
)
from cataloging_service.api.schemas import (
    ItemSchema, ItemListSchema, ItemVariantSchema, VariantGroupSchema,
    ItemCustomisationSchema
)

bp = Blueprint('item_apis', __name__)

item_service = service_provider.item_service


@bp.route('/v1/sellers/<string:seller_id>/items', methods=['POST'])
@raw_json(ItemCreateRequestValidator)
def create_item(seller_id, parsed_request):
    """
    ---
    consumes:
        - application/json
    produces:
        - application/json
    post:
        tags:
            - Item
        description: Create Item
        parameters:
            - in: path
              name: seller_id
              description: The seller_id for the Item that needs to be created
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/ItemCreateRequestValidator"
        responses:
            201:
                description: Created item object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ItemSchema"
    """
    item_create_request = ItemCreateRequest(seller_id, parsed_request)
    created_item = item_service.create_item(item_create_request)
    return jsonify(ItemSchema().dump(created_item).data), 201


@bp.route('/v1/sellers/<string:seller_id>/items', methods=['GET'])
@query_params(ItemSearchRequestValidator)
def get_items(seller_id, parsed_request):
    """
    ---
    get:
        tags:
            - Item
        description: Get All Items
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch all items for
              required: True
              schema:
                    type: string
            - name: name
              in: query
              description: Name to filter by all items for
              required: False
              schema:
                    type: string
            - name: food_type
              in: query
              description: Filter all items via the food_type attribute
              required: False
              schema:
                    type: string
        description: Get All Items
        responses:
            200:
                description: Get all items.
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: "#/components/schemas/ItemListSchema"
    """
    all_items = item_service.get_items(seller_id=seller_id, filters=parsed_request)
    return jsonify(ItemListSchema().dump(all_items, many=True).data), 200


@bp.route('/v1/sellers/<string:seller_id>/items/<string:item_id>', methods=['PATCH'])
@raw_json(ItemEditRequestValidator)
def edit_item(seller_id, item_id, parsed_request):
    """
    ---
    consumes:
        - application/json
    produces:
        - application/json
    patch:
        tags:
            - Item
        description: Edit Item
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to edit Item for
              required: True
              schema:
                    type: string
            - name: item_id
              in: path
              description: Item Id to edit item for
              required: True
              schema:
                    type: int
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/ItemEditRequestValidator"
        responses:
            200:
                description: Edited Item object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ItemSchema"
    """
    item_edit_request = ItemEditRequest(seller_id, parsed_request)
    edited_item = item_service.edit_item(seller_id, item_id, item_edit_request)
    return jsonify(ItemSchema().dump(edited_item).data), 200


@bp.route('/v1/sellers/<string:seller_id>/side-items', methods=['GET'])
def get_side_items(seller_id):
    """
    ---
    get:
        tags:
            - Item
        description: Get All Items which also are Side Items
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch all side items for
              required: True
              schema:
                    type: string
            - name: name
              in: query
              description: Name to filter by all side items for
              required: False
              schema:
                    type: string       
        description: Get All Side Items
        responses:
            200:
                description: Get all side items.
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: "#/components/schemas/ItemListSchema"
    """
    name = request.args.get('name', '')
    all_side_items = item_service.get_side_items(seller_id=seller_id, name=name)
    return jsonify(ItemListSchema().dump(all_side_items, many=True).data), 200


@bp.route('/v1/sellers/<string:seller_id>/items/<string:item_id>', methods=['GET'])
def get_item(seller_id, item_id):
    """
    ---
    get:
        tags:
            - Item
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch item for
              required: True
              schema:
                  type: string
            - name: item_id
              in: path
              description: Item Id to fetch item for
              required: True
              schema:
                  type: string
        description: Get Item Details
        responses:
            200:
                description: Get item object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ItemSchema"
    """
    item = item_service.get_item(seller_id=seller_id, item_id=item_id)
    return jsonify(ItemSchema().dump(item).data), 200


@bp.route('/v1/sellers/<string:seller_id>/variant-groups', methods=['GET'])
def search_variant_groups(seller_id):
    """
    ---
    get:
        tags:
            - Variant Group
        description: Search All Variant Groups
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch all Variant Groups for
              required: True
              schema:
                    type: string
            - name: name
              in: query
              description: Name to filter by all Variant Groups for
              required: False
              schema:
                    type: string   
            - name: is_customisation
              in: query
              description: Boolean to return is_customisation variant groups
              required: False
              schema:
                    type: boolean
            - name: variant_group_ids
              in: query
              description: Variant Group Ids separated by commas to filter Variant Groups for
              required: False
              schema:
                    type: string   
        description: Search All Variant Groups
        responses:
            200:
                description: Search all variants.
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: "#/components/schemas/VariantGroupSchema"
    """
    name = request.args.get('name', '')
    variant_group_ids = request.args.get('variant_group_ids', [])

    if variant_group_ids:
        variant_group_ids = variant_group_ids.split(',')

    is_customisation = request.args.get('is_customisation', None)

    filtered_variants_groups = item_service.search_variant_groups(
        seller_id=seller_id, is_customisation=is_customisation, name=name, variant_group_ids=variant_group_ids)
    return jsonify(VariantGroupSchema().dump(filtered_variants_groups, many=True).data), 200


@bp.route('/v1/sellers/<string:seller_id>/variant-groups', methods=['POST'])
@raw_json(VariantGroupCreateRequestValidator)
def create_variant_group(seller_id, parsed_request):
    """
    ---
    consumes:
        - application/json
    produces:
        - application/json
    post:
        tags:
            - Variant Group
        description: Create Variant Group
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch all Variant Groups for
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/VariantGroupCreateRequestValidator"
        responses:
            201:
                description: Created variant group object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/VariantGroupSchema"
    """
    variant_group_create_request = VariantGroupCreateRequest(seller_id, parsed_request)
    created_variant_group = item_service.create_variant_group(variant_group_create_request)
    return jsonify(VariantGroupSchema().dump(created_variant_group).data), 201


@bp.route('/v1/sellers/<string:seller_id>/variant-groups/<string:variant_group_id>', methods=['PATCH'])
@raw_json(VariantGroupEditRequestValidator)
def edit_variant_group(seller_id, variant_group_id, parsed_request):
    """
    ---
    consumes:
        - application/json
    produces:
        - application/json
    patch:
        tags:
            - Variant Group
        description: Edit Variant
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to edit variant group for
              required: True
              schema:
                    type: string
            - name: variant_group_id
              in: path
              description: Variant Group Id to edit variant group for
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/VariantGroupEditRequestValidator"
        responses:
            200:
                description: Edited variant group object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/VariantGroupSchema"
    """
    variant_group_edit_request = VariantGroupEditRequest(seller_id, variant_group_id, parsed_request)
    edited_variant_group = item_service.edit_variant_group(variant_group_id, variant_group_edit_request)
    return jsonify(VariantGroupSchema().dump(edited_variant_group).data), 200


@bp.route('/v1/sellers/<string:seller_id>/items/<string:item_id>/item-customisations', methods=['GET'])
def get_item_customisations(seller_id, item_id):
    """
    ---
    get:
        tags:
            - Item
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch item-customisations for
              required: True
              schema:
                  type: string
            - name: item_id
              in: path
              description: Item Id to fetch item-customisations for
              required: True
              schema:
                  type: string
        description: Get Item Customisations of a particular Item
        responses:
            200:
                description: Get item-customisations.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ItemCustomisationItemVariantSchema"
    """
    item_customisations = item_service.get_item_customisations(
        seller_id=seller_id, item_id=item_id)
    return jsonify(ItemCustomisationSchema().dump(item_customisations, many=True).data), 200


@bp.route('/v1/sellers/<string:seller_id>/items/<string:item_id>/item-variants', methods=['GET'])
def get_item_variants(seller_id, item_id):
    """
    ---
    get:
        tags:
            - Item
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to item-variants for
              required: True
              schema:
                  type: string
            - name: item_id
              in: path
              description: Item Id to fetch item-variants for
              required: True
              schema:
                  type: string
        description: Get Item Variants of a particular Item
        responses:
            200:
                description: Get item-variants.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ItemVariantShortSchema"
    """
    item_variants = item_service.get_item_variants(
        seller_id=seller_id, item_id=item_id)
    return jsonify(ItemVariantSchema().dump(item_variants, many=True).data), 200


@bp.route('/v1/sellers/<string:seller_id>/items/<int:item_id>', methods=['DELETE'])
def delete_item(seller_id, item_id):
    """
    ---
    delete:
        tags:
            - Item
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to delete item for
              required: True
              schema:
                  type: string
            - name: item_id
              in: path
              description: Item Id to delete item for
              required: True
              schema:
                  type: int
        description: Delete Item
        responses:
            204:
                description: The resource was deleted successfully.
    """
    item_service.soft_delete_item(item_id=item_id, seller_id=seller_id)
    return jsonify({}), 204
