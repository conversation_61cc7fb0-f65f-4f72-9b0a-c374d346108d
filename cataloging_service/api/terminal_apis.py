from flask import Blueprint, request, jsonify
from marshmallow import Schema, fields, ValidationError
from cataloging_service.domain.terminal_service import TerminalService
from cataloging_service.api.schemas import SellerTemplateSchema, SkuTerminalTypeAssignmentSchema
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.utils.decorators import raw_json
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('terminals', __name__, url_prefix='/api/v1/terminals')
terminal_service = TerminalService()


class SellerTemplateCreateSchema(Schema):
    name = fields.String(required=True)
    code = fields.String()
    description = fields.String()
    default_department_id = fields.Integer()
    auto_create_on_property_launch = fields.Boolean(missing=False)
    template_config = fields.Dict()


class BulkSkuAssignmentSchema(Schema):
    sku_ids = fields.List(fields.String(), required=True)


# Seller Template (Terminal Type) APIs
@bp.route('/types', methods=['GET'])
@raw_json
def get_seller_templates():
    """Get all seller templates (terminal types)"""
    try:
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        templates = terminal_service.get_all_seller_templates(include_inactive)
        return jsonify({
            'terminal_types': SellerTemplateSchema(many=True).dump(templates)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting seller templates")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/types', methods=['POST'])
@raw_json
def create_seller_template():
    """Create seller template (terminal type)"""
    try:
        schema = SellerTemplateCreateSchema()
        data = schema.load(request.get_json())
        
        # Validate configuration
        terminal_service.validate_terminal_type_configuration(data)
        
        template = terminal_service.create_seller_template(data)
        return jsonify({
            'message': 'Terminal type created successfully',
            'terminal_type': SellerTemplateSchema().dump(template)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error creating seller template")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/types/<int:template_id>', methods=['GET'])
@raw_json
def get_seller_template(template_id):
    """Get seller template by ID"""
    try:
        template = terminal_service.get_seller_template(template_id)
        return jsonify({
            'terminal_type': SellerTemplateSchema().dump(template)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting seller template")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/types/<int:template_id>', methods=['PUT'])
@raw_json
def update_seller_template(template_id):
    """Update seller template"""
    try:
        schema = SellerTemplateCreateSchema()
        data = schema.load(request.get_json(), partial=True)
        
        # Validate configuration if provided
        if data:
            terminal_service.validate_terminal_type_configuration(data)
        
        template = terminal_service.update_seller_template(template_id, data)
        return jsonify({
            'message': 'Terminal type updated successfully',
            'terminal_type': SellerTemplateSchema().dump(template)
        }), 200
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error updating seller template")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/types/<int:template_id>/activate', methods=['POST'])
@raw_json
def activate_seller_template(template_id):
    """Activate seller template"""
    try:
        template = terminal_service.activate_seller_template(template_id)
        return jsonify({
            'message': 'Terminal type activated successfully',
            'terminal_type': SellerTemplateSchema().dump(template)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error activating seller template")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/types/<int:template_id>/deactivate', methods=['POST'])
@raw_json
def deactivate_seller_template(template_id):
    """Deactivate seller template"""
    try:
        template = terminal_service.deactivate_seller_template(template_id)
        return jsonify({
            'message': 'Terminal type deactivated successfully',
            'terminal_type': SellerTemplateSchema().dump(template)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error deactivating seller template")
        return jsonify({'error': 'Internal server error'}), 500


# SKU Terminal Type Assignment APIs
@bp.route('/types/<int:terminal_type_id>/skus', methods=['GET'])
@raw_json
def get_skus_for_terminal_type(terminal_type_id):
    """Get all SKUs assigned to a terminal type"""
    try:
        skus = terminal_service.get_skus_for_terminal_type(terminal_type_id)
        return jsonify({
            'skus': [{'id': sku.id, 'name': sku.name} for sku in skus]
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting SKUs for terminal type")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/types/<int:terminal_type_id>/skus', methods=['POST'])
@raw_json
def bulk_assign_skus_to_terminal_type(terminal_type_id):
    """Bulk assign SKUs to terminal type"""
    try:
        schema = BulkSkuAssignmentSchema()
        data = schema.load(request.get_json())
        
        assignments = terminal_service.bulk_assign_skus_to_terminal_type(terminal_type_id, data['sku_ids'])
        return jsonify({
            'message': f'Successfully assigned {len(assignments)} SKUs to terminal type',
            'assignments': SkuTerminalTypeAssignmentSchema(many=True).dump(assignments)
        }), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid input data', 'details': e.messages}), 400
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error bulk assigning SKUs to terminal type")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/skus/<string:sku_id>/types', methods=['GET'])
@raw_json
def get_terminal_types_for_sku(sku_id):
    """Get terminal types that can sell a specific SKU"""
    try:
        terminal_types = terminal_service.get_terminal_types_for_sku(sku_id)
        return jsonify({
            'terminal_types': SellerTemplateSchema(many=True).dump(terminal_types)
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error getting terminal types for SKU")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/skus/<string:sku_id>/types/<int:terminal_type_id>', methods=['POST'])
@raw_json
def add_sku_terminal_type_assignment(sku_id, terminal_type_id):
    """Add SKU to terminal type"""
    try:
        assignment = terminal_service.add_sku_terminal_type_assignment(sku_id, terminal_type_id)
        return jsonify({
            'message': 'SKU added to terminal type successfully',
            'assignment': SkuTerminalTypeAssignmentSchema().dump(assignment)
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error adding SKU terminal type assignment")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/skus/<string:sku_id>/types/<int:terminal_type_id>', methods=['DELETE'])
@raw_json
def remove_sku_terminal_type_assignment(sku_id, terminal_type_id):
    """Remove SKU from terminal type"""
    try:
        terminal_service.remove_sku_terminal_type_assignment(sku_id, terminal_type_id)
        return jsonify({'message': 'SKU removed from terminal type successfully'}), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error removing SKU terminal type assignment")
        return jsonify({'error': 'Internal server error'}), 500


# Business Logic Validation APIs
@bp.route('/validate/seller-relation', methods=['POST'])
@raw_json
def validate_terminal_type_seller_relation():
    """Validate terminal type-seller relation"""
    try:
        data = request.get_json()
        property_id = data.get('property_id')
        seller_id = data.get('seller_id')
        terminal_type_id = data.get('terminal_type_id')
        
        if not all([property_id, seller_id, terminal_type_id]):
            return jsonify({'error': 'property_id, seller_id, and terminal_type_id are required'}), 400
        
        is_valid = terminal_service.validate_terminal_type_seller_relation(property_id, seller_id, terminal_type_id)
        return jsonify({
            'valid': is_valid,
            'message': 'Terminal type-seller validation passed'
        }), 200
    except CatalogingServiceException as e:
        return jsonify({'error': e.message, 'valid': False}), e.status_code
    except Exception as e:
        logger.exception("Error validating terminal type-seller relation")
        return jsonify({'error': 'Internal server error'}), 500


@bp.route('/properties/<string:property_id>/setup', methods=['POST'])
@raw_json
def setup_terminal_types_for_property(property_id):
    """Setup terminal types for property launch"""
    try:
        data = request.get_json() or {}
        terminal_type_ids = data.get('terminal_type_ids', [])
        
        results = []
        for terminal_type_id in terminal_type_ids:
            assignments = terminal_service.setup_terminal_type_for_property_launch(property_id, terminal_type_id)
            results.append({
                'terminal_type_id': terminal_type_id,
                'assignments': SkuTerminalTypeAssignmentSchema(many=True).dump(assignments),
                'count': len(assignments)
            })
        
        return jsonify({
            'message': 'Terminal types setup completed for property',
            'results': results
        }), 201
    except CatalogingServiceException as e:
        return jsonify({'error': e.message}), e.status_code
    except Exception as e:
        logger.exception("Error setting up terminal types for property")
        return jsonify({'error': 'Internal server error'}), 500
