from marshmallow import Schema, fields
from cataloging_service.api.schemas import PrinterConfigSchema

class SellerStateResponseSchema(Schema):
    id = fields.Integer()
    code = fields.Integer()
    name = fields.String()


class SellerCityResponseSchema(Schema):
    id = fields.Integer()
    name = fields.String()
    state = fields.Nested(SellerStateResponseSchema)


class SellerConfigSchema(Schema):
    acceptance_time = fields.String()
    delivery_time = fields.String()
    settlement_time = fields.String()
    priority_multiplier = fields.String()
    cancellation_time_after_order_item_created = fields.String()
    can_cancel_ready_items = fields.Boolean()
    can_cancel_served_items = fields.Boolean()
    generate_kot_only_when_item_is_preparing = fields.Boolean()
    printer_config = fields.Nested(PrinterConfigSchema, many=True)


class SellerResponseSchema(Schema):
    id = fields.Integer()
    seller_id = fields.String()
    name = fields.String()
    property_id = fields.String()
    seller_category_id = fields.Integer()
    city = fields.Nested(SellerCityResponseSchema)
    legal_city = fields.Nested(SellerCityResponseSchema)
    gstin = fields.String()
    legal_name = fields.String()
    legal_address = fields.String()
    legal_pincode = fields.String()
    phone_number = fields.String()
    status = fields.String()
    pincode = fields.String()
    base_currency_code = fields.String()
    timezone = fields.String()
    current_business_date = fields.Date()
    seller_config = fields.Nested(SellerConfigSchema)


class SellerSkuResponseSchema(Schema):
    id = fields.Integer()
    sku_id = fields.Integer()
    seller_id = fields.String()
    sku_category_code = fields.String()
    name = fields.String()
    display_name = fields.String()
    description = fields.String()
    is_active = fields.Boolean()
    is_sellable = fields.Boolean()
    menu_category_id = fields.Integer()
    pretax_price = fields.Decimal(precision=10, scale=2)
    icon = fields.String()


class MenuCategoryResponseSchema(Schema):
    id = fields.Integer()
    name = fields.String()
    color = fields.String()
    icon = fields.String()
    seller_category_id = fields.Integer()


class RestaurantTablesResponseSchema(Schema):
    table_number = fields.String()


class TenantConfigResponseSchema(Schema):
    config_name = fields.String()
    config_value = fields.String()
    value_type = fields.String()


class CurrencyConversionRateResponseSchema(Schema):
    conversion_rate = fields.String()
    from_currency = fields.String()
    to_currency = fields.String()
    property_id = fields.String()
    start_date = fields.Date()
    end_date = fields.Date()


class UserDefinedEnumValueResponseSchema(Schema):
    value = fields.String()
    label = fields.String()


class UserDefinedEnumResponseSchema(Schema):
    enum_name = fields.String()
    label = fields.String()
    enum_values = fields.Nested(UserDefinedEnumValueResponseSchema, many=True)
