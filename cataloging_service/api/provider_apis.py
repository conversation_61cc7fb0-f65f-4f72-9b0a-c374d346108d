import logging

from flask import Blueprint, jsonify, request

from cataloging_service.api.schemas import ProviderSchema, PropertySchema
from cataloging_service.domain import service_provider
from cataloging_service.extensions import cache
from cataloging_service.models import Provider, ProviderBrandMapping

bp = Blueprint('provider_apis', __name__)

logger = logging.getLogger('exceptions')

provider_service = service_provider.provider_service


@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={
    Provider: lambda x: x.code,
    ProviderBrandMapping: lambda x: x.provider.code})
@cache.memoize_unhashed_key(attribute_for_cache_key='provider_code', timeout=3600)
def get_cached_provider(provider_code):
    provider = provider_service.sget_provider(provider_code)
    return ProviderSchema().dump(provider).data


@bp.route('/providers/<string:provider_code>', methods=['GET'])
def get_provider(provider_code):
    # TODO: 0 calls
    return jsonify(get_cached_provider(provider_code)), 200


@bp.route('/providers/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={Provider: lambda x: None,
                                                                        ProviderBrandMapping: lambda x: None})
@cache.memoize_unhashed_key(timeout=3600)
def get_all_providers():
    # TODO: 0 calls
    providers = provider_service.sget_all_providers()
    return jsonify(ProviderSchema().dump(providers, many=True).data), 200


@bp.route('/providers/<string:provider_code>/properties', methods=['GET'])
def get_provider_properties(provider_code):
    # TODO: 0 calls
    provider_hotel_codes = request.args.get('provhtl_ids')
    if provider_hotel_codes:
        provider_hotel_codes = provider_hotel_codes.split(',')
    properties = provider_service.sget_all_properties_by_provider(provider_code, provider_hotel_codes)
    payload = PropertySchema().dump(properties, many=True).data if properties else []
    return jsonify(payload), 200
