from flask import Blueprint, jsonify

from cataloging_service.api.schemas import HotelOwnerResponseSchema
from cataloging_service.api.validators import HotelOwnerDetialsValidator
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import raw_json

bp = Blueprint('hotel_owner_apis', __name__)

property_service = service_provider.property_service


@bp.route('/v1/hotel/<string:property_id>/owners', methods=['GET'])
def get_hotel_owner(property_id):
    owner_details = property_service.get_hotel_owner(property_id)
    payload = HotelOwnerResponseSchema().dump(owner_details, many=True).data if owner_details else []
    return jsonify({'owner_details': payload}), 200


@bp.route('/v1/hotel/<string:property_id>/owners', methods=['POST'])
@raw_json(HotelOwnerDetialsValidator)
def udpate_hotel_owner_details(property_id, parsed_request):
    owner_details = property_service.update_hotel_owner_details(property_id, parsed_request)
    payload = HotelOwnerResponseSchema().dump(owner_details, many=True).data if owner_details else []
    return jsonify({'owner_details': payload}), 200
