import datetime

from flask import Blueprint, jsonify
from flask import request
from treebo_commons.utils import dateutils

from cataloging_service.api.request_objects import CurrencyConversionRateRequest
from cataloging_service.api.response_schema import CurrencyConversionRateResponseSchema
from cataloging_service.api.validators import CurrencyConversionRateRequestValidator
from cataloging_service.constants import error_codes
from cataloging_service.domain import service_provider
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import raw_json

currency_conv_rates_bp = Blueprint('CurrencyConversionRates', __name__, url_prefix='/v1')
currency_conversion_rate_service = service_provider.currency_conversion_rate_service


@currency_conv_rates_bp.route('/currency-conversion-rates', methods=['GET'])
def get_currency_conversion_rates():
    """Fetch currency conversion rates details
    ---
    operationId: get_currency_conversion_rates
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get list of all current currency conversion rate
        tags:
            - Currency Conversion Rates
        responses:
            200:
                description: List of currency conversion rate
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/definitions/CurrencyConversionRateResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    property_id = request.args.get('property_id', None)
    if not property_id:
        raise CatalogingServiceException(error_codes.PROPERTY_ID_NOT_GIVEN,
                                         send_error_mail=False)

    from_currency = request.args.get('from_currency', None)

    transaction_date = request.args.get('transaction_date', str(dateutils.current_date()))
    if transaction_date:
        try:
            datetime.datetime.strptime(transaction_date, '%Y-%m-%d')
        except Exception as e:
            raise CatalogingServiceException(error_codes.INVALID_TRANSACTION_DATE_FORMAT,
                                             context='transaction date: %s' % transaction_date,
                                             send_error_mail=False)

    currency_conv_rates_details = currency_conversion_rate_service.get_currency_conversion_rates_details(property_id,
                                                                                                         from_currency,
                                                                                                         transaction_date)
    response = CurrencyConversionRateResponseSchema(many=True).dump(currency_conv_rates_details)
    return jsonify(response.data), 200


@currency_conv_rates_bp.route('/currency-conversion-rates', methods=['POST'])
@raw_json(CurrencyConversionRateRequestValidator)
def create_currency_conversion_rate(parsed_request):
    """create currency conversion rate
    ---
    consumes:
        - application/json
    produces:
        - application/json
    post:
        tags:
            - Currency Conversion Rates
        description: Create Currency Conversion Rate
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/CurrencyConversionRequestValidator"
        responses:
            201:
                description: Created Currency Conversion Rate object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/CurrencyConversionRateResponseSchema"
    """
    currency_conversion_create_request = CurrencyConversionRateRequest(parsed_request)
    currency_conversion_rate = currency_conversion_rate_service.create_currency_conversion_rate(
        currency_conversion_create_request)
    response = CurrencyConversionRateResponseSchema().dump(currency_conversion_rate)
    return jsonify(response.data), 201
