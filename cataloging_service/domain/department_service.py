from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class DepartmentService:
    """Service for department management operations"""

    def __init__(self, department_repository, property_repository, messaging_service):
        self.__department_repository = department_repository
        self.__property_repository = property_repository
        self.__messaging_service = messaging_service

    # Department operations
    def get_all_departments(self, include_inactive=False):
        """Get all tenant-level departments"""
        return self.__department_repository.get_all_departments(include_inactive)

    def get_department(self, department_id):
        """Get department by ID"""
        department = self.__department_repository.get_department_by_id(department_id)
        if not department:
            raise CatalogingServiceException(error_codes.DEPARTMENT_NOT_FOUND)
        return department

    def get_department_by_code(self, code):
        """Get department by code"""
        department = self.__department_repository.get_department_by_code(code)
        if not department:
            raise CatalogingServiceException(error_codes.DEPARTMENT_NOT_FOUND)
        return department

    def get_departments_hierarchy(self, parent_id=None):
        """Get departments in hierarchical structure"""
        return self.__department_repository.get_departments_hierarchy(parent_id)

    def create_department(self, department_data):
        """Create new department"""
        # Validate parent department if specified
        if department_data.get('parent_id'):
            parent = self.__department_repository.get_department_by_id(department_data['parent_id'])
            if not parent:
                raise CatalogingServiceException(error_codes.PARENT_DEPARTMENT_NOT_FOUND)
            if not parent.is_active:
                raise CatalogingServiceException(error_codes.PARENT_DEPARTMENT_INACTIVE)

        # Check if code already exists
        existing = self.__department_repository.get_department_by_code(department_data['code'])
        if existing:
            raise CatalogingServiceException(error_codes.DEPARTMENT_CODE_ALREADY_EXISTS)

        return self.__department_repository.create_department(department_data)

    def update_department(self, department_id, update_data):
        """Update department"""
        # Validate parent department if being updated
        if 'parent_id' in update_data and update_data['parent_id']:
            if update_data['parent_id'] == department_id:
                raise CatalogingServiceException(error_codes.DEPARTMENT_CANNOT_BE_PARENT_OF_ITSELF)

            parent = self.__department_repository.get_department_by_id(update_data['parent_id'])
            if not parent:
                raise CatalogingServiceException(error_codes.PARENT_DEPARTMENT_NOT_FOUND)
            if not parent.is_active:
                raise CatalogingServiceException(error_codes.PARENT_DEPARTMENT_INACTIVE)

        # Check if code already exists (if being updated)
        if 'code' in update_data:
            existing = self.__department_repository.get_department_by_code(update_data['code'])
            if existing and existing.id != department_id:
                raise CatalogingServiceException(error_codes.DEPARTMENT_CODE_ALREADY_EXISTS)

        return self.__department_repository.update_department(department_id, update_data)

    def delete_department(self, department_id):
        """Delete department (soft delete)"""
        return self.__department_repository.delete_department(department_id)

    # Brand Department Mapping operations
    def get_brand_departments(self, brand_id):
        """Get all departments mapped to a brand"""
        return self.__department_repository.get_brand_departments(brand_id)

    def add_brand_department_mapping(self, brand_id, department_id):
        """Add department to brand"""
        # Validate department exists and is active
        department = self.get_department(department_id)
        if not department.is_active:
            raise CatalogingServiceException(error_codes.DEPARTMENT_INACTIVE)

        return self.__department_repository.create_brand_department_mapping(brand_id, department_id)

    def remove_brand_department_mapping(self, brand_id, department_id):
        """Remove department from brand"""
        return self.__department_repository.remove_brand_department_mapping(brand_id, department_id)

    # Property Department operations
    def get_property_departments(self, property_id, include_inactive=False):
        """Get all departments for a property"""
        return self.__department_repository.get_property_departments(property_id, include_inactive)

    def get_property_department(self, property_department_id):
        """Get property department by ID"""
        property_department = self.__department_repository.get_property_department_by_id(property_department_id)
        if not property_department:
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_NOT_FOUND)
        return property_department

    def create_property_department(self, property_id, department_data):
        """Create property department"""
        # Validate property exists
        property_obj = self.__property_repository.get_property(property_id)
        if not property_obj:
            raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND)

        # Check if code already exists for this property
        existing = self.__department_repository.get_property_department_by_code(
            property_id, department_data['code']
        )
        if existing:
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_CODE_EXISTS)

        # Validate tenant department if specified
        if department_data.get('tenant_department_id'):
            tenant_dept = self.get_department(department_data['tenant_department_id'])
            if not tenant_dept.is_active:
                raise CatalogingServiceException(error_codes.TENANT_DEPARTMENT_INACTIVE)

        # Validate parent property department if specified
        if department_data.get('parent_id'):
            parent = self.__department_repository.get_property_department_by_id(department_data['parent_id'])
            if not parent or parent.property_id != property_id:
                raise CatalogingServiceException(error_codes.PARENT_PROPERTY_DEPARTMENT_NOT_FOUND)

        department_data['property_id'] = property_id
        return self.__department_repository.create_property_department(department_data)

    def update_property_department(self, property_department_id, update_data):
        """Update property department"""
        property_department = self.get_property_department(property_department_id)

        # Validate tenant department if being updated
        if 'tenant_department_id' in update_data and update_data['tenant_department_id']:
            tenant_dept = self.get_department(update_data['tenant_department_id'])
            if not tenant_dept.is_active:
                raise CatalogingServiceException(error_codes.TENANT_DEPARTMENT_INACTIVE)

        # Validate parent property department if being updated
        if 'parent_id' in update_data and update_data['parent_id']:
            if update_data['parent_id'] == property_department_id:
                raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_CANNOT_BE_PARENT_OF_ITSELF)

            parent = self.__department_repository.get_property_department_by_id(update_data['parent_id'])
            if not parent or parent.property_id != property_department.property_id:
                raise CatalogingServiceException(error_codes.PARENT_PROPERTY_DEPARTMENT_NOT_FOUND)

        # Check if code already exists for this property (if being updated)
        if 'code' in update_data:
            existing = self.__department_repository.get_property_department_by_code(
                property_department.property_id, update_data['code']
            )
            if existing and existing.id != property_department_id:
                raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_CODE_EXISTS)

        return self.__department_repository.update_property_department(property_department_id, update_data)

    def setup_property_departments_from_brand(self, property_id, brand_id):
        """Setup property departments based on brand mappings"""
        # Get brand departments
        brand_departments = self.get_brand_departments(brand_id)

        created_departments = []
        for mapping in brand_departments:
            department = mapping.department
            if department.auto_create_on_property_launch:
                # Check if department already exists for this property
                existing = self.__department_repository.get_property_department_by_code(
                    property_id, department.code
                )
                if not existing:
                    property_dept_data = {
                        'property_id': property_id,
                        'tenant_department_id': department.id,
                        'name': department.name,
                        'code': department.code,
                        'description': department.description,
                        'financial_code': department.financial_code,
                        'is_active': True,
                        'is_custom': False
                    }
                    created_dept = self.__department_repository.create_property_department(property_dept_data)
                    created_departments.append(created_dept)

        return created_departments

    # SKU Department operations
    def get_sku_departments(self, sku_id):
        """Get departments mapped to a SKU"""
        return self.__department_repository.get_sku_departments(sku_id)

    def add_sku_department_mapping(self, sku_id, department_id):
        """Add SKU to department"""
        # Validate department exists and is active
        department = self.get_department(department_id)
        if not department.is_active:
            raise CatalogingServiceException(error_codes.DEPARTMENT_INACTIVE)

        return self.__department_repository.create_sku_department_mapping(sku_id, department_id)

    # Property SKU Department operations
    def get_property_sku_departments(self, property_id, sku_id=None):
        """Get property-SKU-department mappings"""
        return self.__department_repository.get_property_sku_departments(property_id, sku_id)

    def add_property_sku_department_mapping(self, property_id, sku_id, property_department_id):
        """Add property-SKU-department mapping"""
        # Validate property department exists and belongs to the property
        property_dept = self.get_property_department(property_department_id)
        if property_dept.property_id != property_id:
            raise CatalogingServiceException(error_codes.PROPERTY_DEPARTMENT_PROPERTY_MISMATCH)

        return self.__department_repository.create_property_sku_department_mapping(
            property_id, sku_id, property_department_id
        )

    # Seller Template operations
    def get_all_seller_templates(self, include_inactive=False):
        """Get all seller templates"""
        return self.__department_repository.get_all_seller_templates(include_inactive)

    def get_seller_template(self, template_id):
        """Get seller template by ID"""
        template = self.__department_repository.get_seller_template_by_id(template_id)
        if not template:
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_NOT_FOUND)
        return template

    def create_seller_template(self, template_data):
        """Create seller template"""
        # Validate default department if specified
        if template_data.get('default_department_id'):
            department = self.get_department(template_data['default_department_id'])
            if not department.is_active:
                raise CatalogingServiceException(error_codes.DEPARTMENT_INACTIVE)

        return self.__department_repository.create_seller_template(template_data)

    def update_seller_template(self, template_id, update_data):
        """Update seller template"""
        # Validate default department if being updated
        if 'default_department_id' in update_data and update_data['default_department_id']:
            department = self.get_department(update_data['default_department_id'])
            if not department.is_active:
                raise CatalogingServiceException(error_codes.DEPARTMENT_INACTIVE)

        return self.__department_repository.update_seller_template(template_id, update_data)

    # Business Logic Validation
    def validate_sku_ownership(self, property_id, sku_id, seller_id):
        """Validate SKU ownership rules"""
        return self.__department_repository.validate_sku_ownership(property_id, sku_id, seller_id)
