from cataloging_service.infrastructure.repositories.financial_repository import FinancialRepository
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class FinancialService:
    """Service for financial operations - Transaction Master and Default Mappings"""

    def __init__(self):
        self.__financial_repository = FinancialRepository()

    # Transaction Master operations
    def get_all_transaction_masters(self, entity_type=None, status=None):
        """Get all transaction masters with optional filtering"""
        return self.__financial_repository.get_all_transaction_masters(entity_type, status)

    def get_transaction_master(self, transaction_id):
        """Get transaction master by ID"""
        transaction = self.__financial_repository.get_transaction_master_by_id(transaction_id)
        if not transaction:
            raise CatalogingServiceException(error_codes.TRANSACTION_MASTER_NOT_FOUND)
        return transaction

    def get_transaction_master_by_code(self, transaction_code):
        """Get transaction master by code"""
        transaction = self.__financial_repository.get_transaction_master_by_code(transaction_code)
        if not transaction:
            raise CatalogingServiceException(error_codes.TRANSACTION_MASTER_NOT_FOUND)
        return transaction

    def create_transaction_master(self, transaction_data):
        """Create transaction master"""
        # Generate transaction code if not provided
        if 'transaction_code' not in transaction_data:
            transaction_data['transaction_code'] = self.__financial_repository.generate_transaction_code(
                transaction_data['entity_type'], 
                transaction_data.get('operational_unit_id')
            )
        
        return self.__financial_repository.create_transaction_master(transaction_data)

    def update_transaction_master(self, transaction_id, update_data):
        """Update transaction master"""
        return self.__financial_repository.update_transaction_master(transaction_id, update_data)

    def auto_generate_transaction_codes_for_entity(self, entity_type, entity_id, property_id=None, manage_franchiser_finance=False):
        """Auto-generate transaction codes for entity creation/activation"""
        transactions = []
        
        # Generate base transaction
        base_transaction_data = {
            'entity_type': entity_type,
            'transaction_type': 'ACTIVATION',
            'operational_unit_id': property_id or entity_id,
            'operational_unit_type': 'PROPERTY' if property_id else entity_type,
            'source': 'AUTO_GENERATED',
            'status': 'ACTIVE',
            'hotel_entity_id': property_id
        }
        
        # Create hotel entity transaction
        hotel_transaction = self.create_transaction_master({
            **base_transaction_data,
            'name': f'{entity_type} Activation - Hotel Entity',
            'particulars': f'Auto-generated transaction for {entity_type} {entity_id} activation'
        })
        transactions.append(hotel_transaction)
        
        # Create franchiser entity transaction if dual accounting is enabled
        if manage_franchiser_finance:
            franchiser_transaction = self.create_transaction_master({
                **base_transaction_data,
                'name': f'{entity_type} Activation - Franchiser Entity',
                'particulars': f'Auto-generated franchiser transaction for {entity_type} {entity_id} activation',
                'franchiser_entity_id': property_id,
                'hotel_entity_id': None
            })
            transactions.append(franchiser_transaction)
        
        return transactions

    # Transaction Default Mapping operations
    def get_transaction_default_mappings(self, entity_type=None):
        """Get transaction default mappings"""
        return self.__financial_repository.get_transaction_default_mappings(entity_type)

    def create_transaction_default_mapping(self, mapping_data):
        """Create transaction default mapping"""
        return self.__financial_repository.create_transaction_default_mapping(mapping_data)

    def get_default_mapping_for_entity(self, entity_type, entity_category, transaction_entity_type):
        """Get default mapping for specific entity"""
        return self.__financial_repository.get_default_mapping_for_entity(
            entity_type, entity_category, transaction_entity_type
        )

    def apply_default_mapping_to_transaction(self, transaction_data, entity_type, entity_category):
        """Apply default mapping to transaction data"""
        # Get default mapping for hotel entity
        hotel_mapping = self.get_default_mapping_for_entity(entity_type, entity_category, 'Hotel')
        if hotel_mapping:
            transaction_data.setdefault('gl_code', hotel_mapping.default_gl_code)
            transaction_data.setdefault('erp_id', hotel_mapping.default_erp_id)
            transaction_data.setdefault('particulars', hotel_mapping.default_particulars)
            transaction_data.setdefault('is_merge', hotel_mapping.default_is_merge)
        
        return transaction_data

    def generate_transaction_code_with_defaults(self, entity_type, entity_id, entity_category=None):
        """Generate transaction code and apply default mappings"""
        transaction_code = self.__financial_repository.generate_transaction_code(entity_type, entity_id)
        
        # Apply default mappings if category is provided
        transaction_data = {'transaction_code': transaction_code}
        if entity_category:
            transaction_data = self.apply_default_mapping_to_transaction(
                transaction_data, entity_type, entity_category
            )
        
        return transaction_data
