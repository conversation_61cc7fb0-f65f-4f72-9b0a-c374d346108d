from cataloging_service.infrastructure.repositories.terminal_repository import TerminalRepository
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class TerminalService:
    """Service for terminal/seller template operations"""

    def __init__(self):
        self.__terminal_repository = TerminalRepository()

    # Seller Template (Terminal Type) operations
    def get_all_seller_templates(self, include_inactive=False):
        """Get all seller templates"""
        return self.__terminal_repository.get_all_seller_templates(include_inactive)

    def get_seller_template(self, template_id):
        """Get seller template by ID"""
        template = self.__terminal_repository.get_seller_template_by_id(template_id)
        if not template:
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_NOT_FOUND)
        return template

    def get_seller_template_by_code(self, code):
        """Get seller template by code"""
        template = self.__terminal_repository.get_seller_template_by_code(code)
        if not template:
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_NOT_FOUND)
        return template

    def create_seller_template(self, template_data):
        """Create seller template"""
        # Validate required fields
        if 'name' not in template_data:
            raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        # Generate code if not provided
        if 'code' not in template_data:
            template_data['code'] = template_data['name'].lower().replace(' ', '_')
        
        # Set defaults
        template_data.setdefault('is_active', True)
        template_data.setdefault('auto_create_on_property_launch', False)
        
        # Validate default department if provided
        if template_data.get('default_department_id'):
            from cataloging_service.domain.department_service import DepartmentService
            dept_service = DepartmentService()
            department = dept_service.get_department(template_data['default_department_id'])
            if not department.is_active:
                raise CatalogingServiceException(error_codes.DEPARTMENT_INACTIVE)
        
        return self.__terminal_repository.create_seller_template(template_data)

    def update_seller_template(self, template_id, update_data):
        """Update seller template"""
        # Validate default department if being updated
        if update_data.get('default_department_id'):
            from cataloging_service.domain.department_service import DepartmentService
            dept_service = DepartmentService()
            department = dept_service.get_department(update_data['default_department_id'])
            if not department.is_active:
                raise CatalogingServiceException(error_codes.DEPARTMENT_INACTIVE)

        return self.__terminal_repository.update_seller_template(template_id, update_data)

    def activate_seller_template(self, template_id):
        """Activate seller template"""
        return self.update_seller_template(template_id, {'is_active': True})

    def deactivate_seller_template(self, template_id):
        """Deactivate seller template"""
        return self.update_seller_template(template_id, {'is_active': False})

    # SKU Terminal Type Assignment operations
    def get_sku_terminal_type_assignments(self, sku_id):
        """Get terminal type assignments for a SKU"""
        return self.__terminal_repository.get_sku_terminal_type_assignments(sku_id)

    def add_sku_terminal_type_assignment(self, sku_id, terminal_type_template_id):
        """Add SKU to terminal type"""
        # Validate terminal type template exists and is active
        template = self.get_seller_template(terminal_type_template_id)
        if not template.is_active:
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_NOT_FOUND)
        
        return self.__terminal_repository.create_sku_terminal_type_assignment(sku_id, terminal_type_template_id)

    def remove_sku_terminal_type_assignment(self, sku_id, terminal_type_template_id):
        """Remove SKU from terminal type"""
        return self.__terminal_repository.remove_sku_terminal_type_assignment(sku_id, terminal_type_template_id)

    def get_terminal_types_for_sku(self, sku_id):
        """Get all terminal types that can sell a specific SKU"""
        return self.__terminal_repository.get_terminal_types_for_sku(sku_id)

    def get_skus_for_terminal_type(self, terminal_type_id):
        """Get all SKUs assigned to a specific terminal type"""
        return self.__terminal_repository.get_skus_for_terminal_type(terminal_type_id)

    def bulk_assign_skus_to_terminal_type(self, terminal_type_id, sku_ids):
        """Bulk assign multiple SKUs to a terminal type"""
        # Validate terminal type exists and is active
        template = self.get_seller_template(terminal_type_id)
        if not template.is_active:
            raise CatalogingServiceException(error_codes.SELLER_TEMPLATE_NOT_FOUND)
        
        assignments = []
        for sku_id in sku_ids:
            try:
                assignment = self.add_sku_terminal_type_assignment(sku_id, terminal_type_id)
                assignments.append(assignment)
            except CatalogingServiceException as e:
                # Log error but continue with other SKUs
                logger.warning(f"Failed to assign SKU {sku_id} to terminal type {terminal_type_id}: {e}")
                continue
        
        return assignments

    def setup_terminal_type_for_property_launch(self, property_id, terminal_type_id):
        """Setup terminal type assignments for property launch"""
        # Get all SKUs that should be auto-assigned to this terminal type
        template = self.get_seller_template(terminal_type_id)
        
        # Get SKUs that have this terminal type as their default
        from cataloging_service.models import Sku
        from cataloging_service.infrastructure.repositories.repository import BaseRepository
        repo = BaseRepository()
        
        auto_assign_skus = repo.session.query(Sku).filter(
            Sku.seller_template_id == terminal_type_id,
            Sku.is_active == True
        ).all()
        
        assignments = []
        for sku in auto_assign_skus:
            try:
                assignment = self.add_sku_terminal_type_assignment(sku.id, terminal_type_id)
                assignments.append(assignment)
            except CatalogingServiceException:
                # Assignment might already exist, continue
                continue
        
        return assignments

    # Business Logic Validation
    def validate_terminal_type_seller_relation(self, property_id, seller_id, terminal_type_id):
        """Validate unique terminal type per seller per property"""
        return self.__terminal_repository.validate_terminal_type_seller_relation(
            property_id, seller_id, terminal_type_id
        )

    def get_available_terminal_types_for_property(self, property_id):
        """Get terminal types available for a property based on brand configuration"""
        # This would integrate with brand-department mapping logic
        # For now, return all active terminal types
        return self.get_all_seller_templates(include_inactive=False)

    def validate_terminal_type_configuration(self, template_data):
        """Validate terminal type template configuration"""
        # Basic validation
        if not template_data.get('name'):
            raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        # Validate template configuration if provided
        if template_data.get('template_config'):
            config = template_data['template_config']
            # Add specific validation rules for template configuration
            if not isinstance(config, dict):
                raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        return True
