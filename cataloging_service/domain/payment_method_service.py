from cataloging_service.infrastructure.repositories.payment_method_repository import PaymentMethodRepository
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
import logging

logger = logging.getLogger(__name__)


class PaymentMethodService:
    """Service for payment method operations"""

    def __init__(self):
        self.__payment_method_repository = PaymentMethodRepository()

    # Tenant-level Payment Method operations
    def get_all_payment_methods(self, include_inactive=False):
        """Get all tenant-level payment methods"""
        return self.__payment_method_repository.get_all_payment_methods(include_inactive)

    def get_payment_method(self, payment_method_id):
        """Get payment method by ID"""
        payment_method = self.__payment_method_repository.get_payment_method_by_id(payment_method_id)
        if not payment_method:
            raise CatalogingServiceException(error_codes.PAYMENT_METHOD_NOT_FOUND)
        return payment_method

    def get_payment_method_by_code(self, code):
        """Get payment method by code"""
        payment_method = self.__payment_method_repository.get_payment_method_by_code(code)
        if not payment_method:
            raise CatalogingServiceException(error_codes.PAYMENT_METHOD_NOT_FOUND)
        return payment_method

    def create_payment_method(self, payment_method_data):
        """Create payment method"""
        # Validate required fields
        if 'name' not in payment_method_data or 'code' not in payment_method_data:
            raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        # Set defaults
        payment_method_data.setdefault('is_active', True)
        payment_method_data.setdefault('auto_create_on_property_launch', False)
        
        return self.__payment_method_repository.create_payment_method(payment_method_data)

    def update_payment_method(self, payment_method_id, update_data):
        """Update payment method"""
        return self.__payment_method_repository.update_payment_method(payment_method_id, update_data)

    def activate_payment_method(self, payment_method_id):
        """Activate payment method"""
        return self.update_payment_method(payment_method_id, {'is_active': True})

    def deactivate_payment_method(self, payment_method_id):
        """Deactivate payment method"""
        return self.update_payment_method(payment_method_id, {'is_active': False})

    # Property Payment Method operations
    def get_property_payment_methods(self, property_id, include_inactive=False):
        """Get all payment methods for a property"""
        return self.__payment_method_repository.get_property_payment_methods(property_id, include_inactive)

    def get_property_payment_method(self, property_payment_method_id):
        """Get property payment method by ID"""
        property_payment_method = self.__payment_method_repository.get_property_payment_method_by_id(property_payment_method_id)
        if not property_payment_method:
            raise CatalogingServiceException(error_codes.PROPERTY_PAYMENT_METHOD_NOT_FOUND)
        return property_payment_method

    def create_property_payment_method(self, property_payment_method_data):
        """Create property payment method"""
        # Validate required fields
        required_fields = ['property_id', 'name', 'code']
        for field in required_fields:
            if field not in property_payment_method_data:
                raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        # Set defaults
        property_payment_method_data.setdefault('is_active', True)
        property_payment_method_data.setdefault('is_custom', True)
        
        return self.__payment_method_repository.create_property_payment_method(property_payment_method_data)

    def update_property_payment_method(self, property_payment_method_id, update_data):
        """Update property payment method"""
        return self.__payment_method_repository.update_property_payment_method(property_payment_method_id, update_data)

    def inherit_payment_methods_for_property(self, property_id):
        """Inherit payment methods from tenant-level definitions for a property"""
        return self.__payment_method_repository.inherit_payment_methods_from_tenant(property_id)

    def setup_property_payment_methods(self, property_id, custom_methods=None):
        """Complete setup of payment methods for a property"""
        # First, inherit from tenant-level
        inherited_methods = self.inherit_payment_methods_for_property(property_id)
        
        # Then, add any custom methods
        custom_created = []
        if custom_methods:
            for method_data in custom_methods:
                method_data['property_id'] = property_id
                method_data['is_custom'] = True
                custom_created.append(self.create_property_payment_method(method_data))
        
        return {
            'inherited_methods': inherited_methods,
            'custom_methods': custom_created,
            'total_count': len(inherited_methods) + len(custom_created)
        }

    def get_payment_methods_for_property_onboarding(self, property_id=None):
        """Get payment methods that should be auto-created for property onboarding"""
        return self.__payment_method_repository.get_all_payment_methods().filter(
            lambda pm: pm.auto_create_on_property_launch and pm.is_active
        )

    def validate_payment_method_configuration(self, payment_method_data):
        """Validate payment method configuration"""
        # Basic validation
        if not payment_method_data.get('name'):
            raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        if not payment_method_data.get('code'):
            raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        # Validate payment method type if provided
        valid_types = ['card', 'cash', 'digital', 'bank_transfer', 'wallet']
        if payment_method_data.get('payment_method_type') and payment_method_data['payment_method_type'] not in valid_types:
            raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        # Validate paid_to values
        valid_paid_to = ['hotel', 'third_party', 'corporate']
        if payment_method_data.get('paid_to') and payment_method_data['paid_to'] not in valid_paid_to:
            raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        # Validate allowed_paid_by values
        valid_paid_by = ['guest', 'corporate', 'staff']
        if payment_method_data.get('allowed_paid_by') and payment_method_data['allowed_paid_by'] not in valid_paid_by:
            raise CatalogingServiceException(error_codes.INVALID_INPUT_DATA)
        
        return True
