FROM python:3.5

ARG req_file=requirements.txt

RUN mkdir -p /usr/src/app /var/log /usr/src/app/resources
WORKDIR /usr/src/app

COPY requirements /usr/src/app/requirements

RUN pip install -r $req_file

COPY . /usr/src/app/

RUN ln -s \
    /usr/local/lib/python3.5/site-packages/flask_admin/static/bootstrap/bootstrap3/swatch/fonts \
    /usr/local/lib/python3.5/site-packages/flask_admin/static/bootstrap/bootstrap3/fonts

EXPOSE 8001
