import logging
import datetime
import traceback
from decimal import Decimal

from treebo_commons.money.constants import CurrencyType

from cataloging_service.api.v3.property_launch import PropertyLaunchSchema
from cataloging_service.constants import constants, error_codes
from cataloging_service.constants.model_choices import PropertyChoices, PropertyDetailChoices, \
    RoomTypeConfigurationChoices, SuperHeroPriceSlabChoices, DS_PRICING_ENABLED_PRICE_SLABS, HygieneShieldNameChoices
from cataloging_service.domain import service_provider
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import Property, Location, PropertyDetail, Room, RoomTypeConfiguration, \
    RoomType, Owner, Ownership, GuestFacingProcess, Brand, PropertyBrandMapping
from cataloging_service.utils import Utils
from core.property._property_amenities import create_property_amenities
from core.property._room_amenities import create_room_amenities

logger = logging.getLogger(__name__)

INDEPENDENT_BRAND_CODE = 'independent'
IS_PARTNER_PRICING_ENABLED = True  # for independent properties


def launch_property(property_launch_serializer: PropertyLaunchSchema):
    try:
        room_types = [{'type': r.type,
                       'id': r.id,
                       'code': r.code} for r in RoomType.query.all()]
        property_launch = property_launch_serializer.data
        property = create_property(property_launch)
        location = create_location(property, property_launch)

        property_detail = create_property_detail(property, property_launch)

        room_type_configs = [create_room_type(property, property_detail.provider, detail, room_types)
                             for detail in property_launch['room_type_details']]

        rooms = [create_room(property, detail, room_types, room_type_configs)
                 for detail in property_launch['room_details']]

        owner_details = create_owner_details(property, property_launch)
        create_suited_for(property, property_launch)
        guest_facing_detail = create_guest_facing_process(property, property_launch)
        room_amentities = [create_room_amenities(property_launch, room, room_types) for room in rooms]
        property_amentities = create_property_amenities(property, property_launch)

        brand = Brand.query.filter(Brand.code == INDEPENDENT_BRAND_CODE).first()

        repo_provider.property_repository.persist(property)
        repo_provider.property_repository.persist(location)
        repo_provider.property_repository.persist(property_detail)
        repo_provider.property_repository.persist_all(room_type_configs)
        repo_provider.property_repository.persist_all(rooms)
        repo_provider.property_repository.persist_all(owner_details)
        repo_provider.property_repository.persist(guest_facing_detail)
        repo_provider.property_repository.persist_all(room_amentities)
        repo_provider.property_repository.persist_all(property_amentities)

        if brand:
            property_brand = PropertyBrandMapping(property_id=property.id, brand_id=brand.id)
            repo_provider.property_repository.persist(property_brand)

        service_provider.property_service.set_amenity_summary(property)
        service_provider.property_service.auto_create_sku_for_given_properties(property.id)
        repo_provider.property_repository.session().commit()
        return property

    except Exception as e:
        logger.exception(e)
        traceback.print_exc()
        raise


def create_owner_details(property, property_launch):
    owner_details = []
    owner = Owner()
    owner.first_name = property_launch['owner_name']
    owner.email = property_launch['owner_email']
    owner.phone_number = property_launch['owner_phone']

    ownership = Ownership()
    ownership.property = property
    ownership.owner = owner
    ownership.primary = True

    owner_details.append(owner)
    owner_details.append(ownership)

    if property_launch['secondary_owner_name']:
        second_owner = Owner()
        second_owner.first_name = property_launch['secondary_owner_name']
        second_owner.email = property_launch['secondary_owner_email']
        second_owner.phone_number = property_launch['secondary_owner_phone']
        owner_details.append(second_owner)
        s_ownership = Ownership()
        s_ownership.owner = second_owner
        s_ownership.property = property
        owner_details.append(s_ownership)

    return owner_details


def create_suited_for(property, property_launch):
    suited_for = property_launch['suited_for'].split(',')
    suited_for = [s for s in suited_for if s]  # weed out empty strings
    for suited_to in suited_for:
        property.guest_types.append(repo_provider.meta_repository.get_guest_type_by_name(suited_to))


def create_guest_facing_process(property, property_launch):
    guest_facing_details = GuestFacingProcess()
    guest_facing_details.property = property

    guest_facing_details.checkin_time = property_launch['standard_checkin_time']
    guest_facing_details.checkout_time = property_launch['standard_checkout_time']
    guest_facing_details.free_early_checkin = property_launch['free_early_checkin_time']
    guest_facing_details.free_late_checkout = property_launch['free_late_checkout_time']
    guest_facing_details.early_checkin_fee = property_launch['early_checkin_fee']
    guest_facing_details.late_checkout_fee = property_launch['late_checkout_fee']
    guest_facing_details.system_freeze_time = property_launch.get('system_freeze_time', GuestFacingProcess.three_pm)
    return guest_facing_details


def create_property(property_launch):
    property_object = Property()
    property_object.name = property_launch['name']
    property_object.old_name = property_launch['previous_name']
    property_object.legal_name = property_launch['legal_name']
    property_object.status = (
        property_launch["status"]
        if property_launch.get("status")
        else PropertyChoices.STATUS_SIGNED
    )
    property_object.signed_date = property_launch['signed_date']
    property_object.contractual_launch_date = property_launch['launch_date']
    property_object.base_currency_code = property_launch.get('base_currency_code') if property_launch.get(
        'base_currency_code') else CurrencyType.INR.value
    property_object.timezone = property_launch.get('timezone')
    property_object.logo = property_launch.get('hotel_logo')
    property_object.country_code = property_launch.get('country_code')

    set_id(property_object)
    property_object.hx_id = property_object.id
    property_object.current_business_date = datetime.date.today()
    return property_object


def set_id(property_object):
    for retry_count in range(constants.MAX_PROPERTY_ID_RETRY):
        pid = Utils.generate_property_id_v2(property_object)
        existing_property = repo_provider.property_repository.get_property(pid)
        if existing_property:
            logger.error('Received integrity error while saving property, %s' % property_object)
            if retry_count + 1 == constants.MAX_PROPERTY_ID_RETRY:
                raise CatalogingServiceException(error_codes.PROPERTY_ID_CLASHED)
        else:
            property_object.id = str(pid)
            break


def create_location(property, property_launch):
    location = Location()
    location.latitude = Utils.quantize_and_round_off(
        Decimal(property_launch['latitude']),
        decimal_places_to_round_off=6
    )
    location.longitude = Utils.quantize_and_round_off(
        Decimal(property_launch['longitude']),
        decimal_places_to_round_off=6
    )

    location.postal_address = property_launch['address']['line1'] + property_launch['address']['line2']
    location.pincode = int(property_launch['address']['pincode'])

    location.city_id = property_launch['address']['city_id']
    # locality = Locality.query.filter(Locality.id == property_launch['address']['locality_id']).one()
    # location.micro_market_id = locality.micro_market_id
    # location.locality_id = locality.id
    location.maps_link = property_launch['google_maps_link']
    location.property_id = property.id
    location.legal_address = property_launch['legal_address']['line1'] + property_launch['legal_address']['line2']

    legal_pincode = property_launch['legal_address']['pincode']
    if legal_pincode:
        location.legal_pincode = int(legal_pincode)

    location.legal_city_id = property_launch['legal_address']['city_id']

    return location


def create_property_detail(property, property_launch):
    property_details = PropertyDetail()
    property_details.neighbourhood_type = PropertyDetailChoices.NEIGHBOURHOOD_OTHERS
    property_details.property_type = PropertyDetailChoices.PROPERTY_TYPE_HOTEL
    property_details.property_style = PropertyDetailChoices.PROPERTY_STYLE_OTHERS
    property_details.building_style = PropertyDetailChoices.BUILDING_FLOORS_IN_EACH_BUILDING
    provider = repo_provider.provider_repository.rget_provider_by_code('treebo')
    property_details.provider = provider
    property_details.legal_signature = '/reseller/Stamp_White.png'

    property_details.floor_count = property_launch['total_floors']
    property_details.star_rating = property_launch.get('star_rating', None)
    property_details.construction_year = 0000
    property_details.is_leased = property_launch['facilities']['is_property_leased']

    property_details.neighbourhood_detail = ''
    property_details.style_detail = ''
    property_details.reception_landline = property_launch['reception_landline']
    property_details.reception_mobile = property_launch['reception_mobile']
    property_details.email = property_launch['email']
    property_details.previous_franchise = True
    property_details.is_partner_pricing_enabled = IS_PARTNER_PRICING_ENABLED
    property_details.unmarried_couple_allowed = property_launch['facilities']['is_unmarried_couple_allowed']
    property_details.local_id_allowed = property_launch['facilities']['is_local_ids_accepted']
    property_details.is_msme = property_launch['is_msme']
    property_details.msme_number = property_launch['msme_number']
    property_details.tan = property_launch['tan']
    property_details.property = property

    superhero_price_slab = property_launch.get('super_hero_price_slab')
    if superhero_price_slab:
        slab = SuperHeroPriceSlabChoices(superhero_price_slab.upper())
        property_details.superhero_price_slab = slab.value
        property_details.is_ds_pricing_enabled = slab in DS_PRICING_ENABLED_PRICE_SLABS

    hygiene_shield_name = property_launch.get('hygiene_shield_name')
    if hygiene_shield_name:
        slab = HygieneShieldNameChoices(hygiene_shield_name.upper())
        property_details.hygiene_shield_name = slab.value
    return property_details


def create_room(property, room_detail, room_types, room_type_configs):
    room = Room()
    room.property = property
    room.room_number = room_detail['room_number']
    room.building_number = room_detail['building_number']
    room.floor_number = room_detail['floor_number']
    room_type = [rt for rt in room_types if rt['code'] == room_detail['room_type'].upper()][0]
    room.room_type_id = room_type['id']
    try:
        room_type_config = [r for r in room_type_configs if r.room_type_id == room.room_type_id][0]
    except IndexError as e:
        raise ValueError("Unsupported room_type give for room: {r}".format(r=room.room_number)) from e
    room.room_type_config = room_type_config
    room.room_size = Decimal(room_type_config.min_room_size)
    return room


def create_room_type(property, provider, room_type_detail, room_types):
    room_config = RoomTypeConfiguration()

    room_type = [rt for rt in room_types if rt['type'] == room_type_detail['room_type'].upper()][0]
    room_config.room_type_id = room_type['id']
    room_config.property_id = property.id
    room_config.provider = provider
    room_config.min_occupancy = 1
    room_config.max_occupancy = '{a}+{c}'.format(a=room_type_detail['max_adults'], c=room_type_detail['max_children'])
    room_config.extra_bed = RoomTypeConfigurationChoices.EXTRA_BED_MATTRESS
    room_config.adults = int(room_type_detail['max_adults'])
    room_config.mm_id = '{pid}-{rt}'.format(pid=property.id, rt=room_type['id'])
    room_config.children = int(room_type_detail['max_children'])
    room_config.max_total = int(room_type_detail['max_total'])
    room_config.ext_room_code = room_type['code']
    room_config.ext_room_name = room_type['type']
    room_config.display_name = room_type['type']
    room_config.min_room_size = Decimal(room_type_detail['room_size'])
    return room_config
