"""
Just for use in swagger
"""

from marshmallow import fields, Schema, ValidationError, post_dump


class APIErrorSchema(Schema):
    title = fields.String()
    code = fields.String()
    detail = fields.String()
    type = fields.String()


class PaginationSchema(Schema):
    total = fields.Integer(dump_to='total_records')
    pages = fields.Integer(dump_to='total_pages')
    per_page = fields.Integer()
    page = fields.Integer(dump_to='current_page')
    next_num = fields.Integer(dump_to='next_page')
    previous_num = fields.Integer(dump_to='previous_page')
    has_next = fields.Boolean()
    has_prev = fields.Boolean()
    max_results_per_page = fields.Integer(default=200)

    class Meta:
        strict = True

    @post_dump
    def validate(self, data):
        if data['per_page'] > data['max_results_per_page']:
            raise ValidationError('Max allowed results per page is: {p}'.format(p=data['max_results_per_page']))

        if data['total_pages'] and data['current_page'] > data['total_pages']:
            # page 1 is not an invalid page if zero pages are there
            raise ValidationError('Invalid page number: {p}'.format(p=data['current_page']))
